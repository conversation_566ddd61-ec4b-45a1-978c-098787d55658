INSERT INTO ins_integrations
(id, internal_reference_id, integration_target_type_code, identifier_json, integration_account_id, integration_target_code, status_code, last_success_sync_time, created_at, updated_at, deleted_at, updated_by, created_by, client_id, "name", data_mapping_code, custom_data_mapping, hide_on_monitoring, machine_status_code, machine_status_last_updated_at, is_enable_command, iccid, show_data_mapping_on_platform, description, show_data_mapping_on_client)
VALUES('ini_21d439af-aeb5-4b53-93e5-9ecac92a6dbf', 'ass_b640002c-4371-4a2d-91b9-aadde53962d4', 'FLESPI_TRACKING', '{"imei": "***************"}'::jsonb, 'ina_df079830-d362-426d-b783-535dc7d48dd1', 'FLESPI', 'ACTIVE', '0001-01-01 06:31:12.000', '2024-08-13 10:58:24.329', '2025-03-12 15:19:03.359', NULL, 'usr_fb57d3e9-531e-4eeb-bc5c-26a87f1a6a59', 'usr_5f7d3b97-47c6-4c1a-8e67-9fe5efcaf8e3', 'cln_450e9098-10ff-4203-b146-c5001f8ae15e', 'TVIP TRUCK HINO 500 B929JYU TELTONIKA FMC 125', 'FLESPI_TELTONIKA', NULL, false, 'UNLOCK', NULL, false, NULL, '{}', NULL, '{time,ident,gps.position_hdop,gps.position_pdop,gps.position_speed,gps.position_valid,gps.vehicle_mileage,general.digital_input,gps.position_altitude,gps.position_latitude,can_bus.can_cng_status,can_bus.can_engine_rpm,can_bus.can_esp_status,can_bus.can_fuel_level,can_bus.can_program_id,can_bus.can_pto_status,general.digital_output,gps.position_direction,gps.position_longitude,can_bus.can_hood_status,general.battery_current,general.battery_voltage,general.movement_status,gps.position_satellites,can_bus.can_trunk_status,general.gsm_signal_level,can_bus.can_cruise_status,can_bus.can_fuel_consumed,can_bus.can_vehicle_speed,can_bus.can_parking_status,can_bus.can_private_status,can_bus.can_webasto_status,can_bus.can_low_beam_status,can_bus.can_vehicle_mileage,can_bus.can_engine_oil_level,can_bus.can_handbrake_status,can_bus.can_high_beam_status,can_bus.can_interlock_active,can_bus.can_car_closed_status,can_bus.can_drive_gear_status,can_bus.can_engine_load_level,can_bus.can_engine_motorhours,can_bus.can_module_sleep_mode,can_bus.can_standalone_engine,can_bus.can_connection_state_1,can_bus.can_connection_state_2,can_bus.can_connection_state_3,can_bus.can_engine_lock_status,can_bus.can_engine_temperature,can_bus.can_pedal_brake_status,can_bus.can_roof_opened_status,can_bus.can_ignition_key_status,can_bus.can_light_signal_status,can_bus.can_neutral_gear_status,can_bus.can_pedal_clutch_status,can_bus.can_reverse_gear_status,can_bus.can_air_condition_status,can_bus.can_eps_indicator_status,can_bus.can_esp_indicator_status,can_bus.can_factory_armed_status,can_bus.can_throttle_pedal_level,can_bus.can_engine_working_status,can_bus.can_parking_lights_status,can_bus.can_rear_left_door_status,can_bus.can_stop_indicator_status,can_bus.can_driver_seatbelt_status,can_bus.can_electric_engine_status,can_bus.can_engine_ignition_status,can_bus.can_front_left_door_status,can_bus.can_front_passenger_status,can_bus.can_manual_retarder_status,can_bus.can_rear_fog_lights_status,can_bus.can_rear_right_door_status,can_bus.can_trip_engine_motorhours,can_bus.can_airbag_indicator_status,can_bus.can_dynamic_ignition_status,can_bus.can_front_fog_lights_status,can_bus.can_front_right_door_status,can_bus.can_operator_present_status,can_bus.can_tracker_counted_mileage,can_bus.can_battery_indicator_status,can_bus.can_car_closed_remote_status,can_bus.can_rear_differential_status,can_bus.can_tire_pressure_low_status,can_bus.can_warning_indicator_status,general.external_powersource_voltage,can_bus.can_automatic_retarder_status,can_bus.can_front_differential_status,can_bus.can_glow_plug_indicator_status,can_bus.can_handbrake_indicator_status,can_bus.can_trailer_axle_lift_status_1,can_bus.can_trailer_axle_lift_status_2,can_bus.can_lights_hazard_lights_status,can_bus.can_maintenance_required_status,can_bus.can_abs_failure_indicator_status,can_bus.can_soot_filter_indicator_status,can_bus.can_additional_rear_lights_status,can_bus.can_check_engine_indicator_status,can_bus.can_oil_pressure_indicator_status,can_bus.can_tracker_counted_fuel_consumed,can_bus.can_additional_front_lights_status,can_bus.can_electronic_power_control_status,can_bus.can_front_passenger_seatbelt_status,can_bus.can_fuel_level_low_indicator_status,can_bus.can_lights_failure_indicator_status,can_bus.can_ready_to_drive_indicator_status,can_bus.can_vehicle_battery_charging_status,can_bus.can_central_differential_4_hi_status,can_bus.can_central_differential_4_lo_status,can_bus.can_driver_seatbelt_indicator_status,can_bus.can_wear_brake_pads_indicator_status,can_bus.can_coolant_level_low_indicator_status,can_bus.can_passenger_seatbelt_indicator_status,can_bus.can_rear_left_passenger_seatbelt_status,can_bus.can_rear_right_passenger_seatbelt_status,can_bus.can_rear_central_passenger_seatbelt_status}');

INSERT INTO ins_integration_devices
(id, integration_type_code, reference_code, integration_id, attached_at, attached_by_user_id, client_id, created_at, updated_at, updated_by, created_by, deleted_at)
VALUES('ide_b6948e7f-6d3c-44ad-b151-dad43a7a71ce', 'DEVICE_TABLET', '1a05af8d10a193aa', 'ini_21d439af-aeb5-4b53-93e5-9ecac92a6dbf', '2025-08-08 14:59:19.434', 'usr_ADMIN_ASSETFINDR', 'cln_450e9098-10ff-4203-b146-c5001f8ae15e', '2025-08-08 14:20:29.424', '2025-08-08 15:00:57.551', 'usr_ADMIN_ASSETFINDR', 'usr_ADMIN_ASSETFINDR', NULL);