package integrationservice

import (
	"assetfindr/internal/infrastructure/bq"
	"assetfindr/internal/infrastructure/cloudStorage"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/internal/infrastructure/email"

	assetPersistence "assetfindr/internal/app/asset/persistence"
	notificationPersistence "assetfindr/internal/app/notification/presistence"
	notificationUsecase "assetfindr/internal/app/notification/usecase"
	taskPersistence "assetfindr/internal/app/task/persistence"
	userIdentityPersistence "assetfindr/internal/app/user-identity/persistence"

	"github.com/gin-gonic/gin"

	trackingPersistence "assetfindr/internal/app/geo/persistence"
	integrationHandlr "assetfindr/internal/app/integration/handler"
	integrationPersistence "assetfindr/internal/app/integration/presistence"
	integrationRouters "assetfindr/internal/app/integration/routers"
	integrationUsecase "assetfindr/internal/app/integration/usecase"
	storagePersistence "assetfindr/internal/app/storage/persistence"

	truphonePersistence "assetfindr/internal/app/truphone/presistence"
)

func InitializeIntegration(bq bq.BQUsecase, dbUsecase, dbTimeScaleUsecase database.DBUsecase, router *gin.Engine) error {
	assetVehicleRepo := assetPersistence.NewAssetVehicleRepository()
	assetRepo := assetPersistence.NewAssetRepository()
	assetAssignmentRepo := assetPersistence.NewAssetAssignmentRepository()
	assetLinkedRepo := assetPersistence.NewAssetLinkedRepository()

	userRepo := userIdentityPersistence.NewUserRepository()
	clientRepo := userIdentityPersistence.NewClientRepository()
	integrationRepository := integrationPersistence.NewIntegrationRepository()
	alertRepository := integrationPersistence.NewAlertRepository()
	tyreAlertRepository := integrationPersistence.NewTyreAlertRepository()
	trackingRepository := trackingPersistence.NewTrackingRepository()
	accurateRepository := integrationPersistence.NewAccurateRepository()
	linkRepository := integrationPersistence.NewLinkRepository()
	wialonRepository := integrationPersistence.NewWialonRepository()
	ticketRepo := taskPersistence.NewTicketRepository()
	storageRepository := storagePersistence.NewStorageRepository(cloudStorage.Bucket)
	emailRepo := notificationPersistence.NewEmailRepository(email.GetEmailService())

	linkUseCase := integrationUsecase.NewLinkUsecase(dbUsecase, linkRepository)
	wialonUseCase := integrationUsecase.NewWialonUsecase(dbUsecase, wialonRepository, userRepo, assetVehicleRepo)

	gpsidRepository := integrationPersistence.NewGPSIDRepository()
	integrationUseCase := integrationUsecase.NewIntegrationUsecase(
		dbUsecase,
		bq,
		dbTimeScaleUsecase,
		integrationRepository,
		gpsidRepository,
		trackingRepository,
		assetVehicleRepo,
		assetAssignmentRepo,
		userRepo,
		assetRepo,
		accurateRepository,
		clientRepo,
		storageRepository,
		alertRepository,
		truphonePersistence.NewTruphoneRepository(),
		emailRepo,
	)
	notificationRepo := notificationPersistence.NewAssetTyreRepository()

	notificationUc := notificationUsecase.NewNotificationUsecase(
		dbUsecase,
		notificationRepo,
		emailRepo,
		userRepo,
	)

	alertUseCase := integrationUsecase.NewAlertUsecase(
		dbUsecase,
		dbTimeScaleUsecase,
		alertRepository,
		gpsidRepository,
		trackingRepository,
		assetVehicleRepo,
		assetAssignmentRepo,
		userRepo,
		assetRepo,
		accurateRepository,
		clientRepo,
		ticketRepo,
		notificationUc,
		assetLinkedRepo,
	)
	tyreAlertUseCase := integrationUsecase.NewTyreAlertUseCase(dbUsecase, tyreAlertRepository, userRepo)
	integrationHandler := integrationHandlr.NewIntegrationHandler(integrationUseCase)
	alertHandler := integrationHandlr.NewAlertHandler(alertUseCase)
	tyreAlertHandler := integrationHandlr.NewTyreAlertHandler(tyreAlertUseCase)
	linkHandler := integrationHandlr.NewLinkHandler(linkUseCase)
	wialonHandler := integrationHandlr.NewWialonHandler(wialonUseCase)

	integrationRouters.RegisterIntegrationRoutes(router, integrationHandler)
	integrationRouters.RegisterVolvoConnectIntegrationRoutes(router, integrationHandler)
	integrationRouters.RegisterVertexAIIntegrationRoutes(router, integrationHandler)
	integrationRouters.RegisterAlertRoutes(router, alertHandler)
	integrationRouters.RegisterTyreAlertRoutes(router, tyreAlertHandler)
	integrationRouters.RegisterLinkRoutes(router, linkHandler)
	integrationRouters.RegisterWialonRoutes(router, wialonHandler)
	return nil
}
