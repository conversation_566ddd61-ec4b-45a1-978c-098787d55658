package inventoryservice

import (
	approvalPersistence "assetfindr/internal/app/approval/persistence"
	approvalUsecase "assetfindr/internal/app/approval/usecase"
	assetPersistence "assetfindr/internal/app/asset/persistence"
	financePresistence "assetfindr/internal/app/finance/presistence"
	financeUseCase "assetfindr/internal/app/finance/usecase"
	integrationPersistence "assetfindr/internal/app/integration/presistence"
	"assetfindr/internal/app/inventory/handler"
	"assetfindr/internal/app/inventory/persistence"
	"assetfindr/internal/app/inventory/routers"
	"assetfindr/internal/app/inventory/usecase"
	notificationPersistence "assetfindr/internal/app/notification/presistence"
	notificationUsecase "assetfindr/internal/app/notification/usecase"
	storagePersistence "assetfindr/internal/app/storage/persistence"
	storageUsecase "assetfindr/internal/app/storage/usecase"
	taskPersistence "assetfindr/internal/app/task/persistence"
	userIdentityPersistence "assetfindr/internal/app/user-identity/persistence"
	"assetfindr/internal/infrastructure/cloudStorage"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/internal/infrastructure/email"

	"github.com/gin-gonic/gin"
)

func InitializeInventory(dbUsecase database.DBUsecase, router *gin.Engine) error {

	// Repostiories
	productRepository := persistence.NewProductRepository()
	orderRepository := persistence.NewOrderRepository()
	packageRepository := persistence.NewPackageRepository()
	inventoryRepository := persistence.NewInventoryRepository()
	inventoryTransactionRepository := persistence.NewInventoryTransactionRepository()
	storageRepository := storagePersistence.NewStorageRepository(cloudStorage.Bucket)
	attachmentRepository := storagePersistence.NewAttachmentRepository()
	locationRepository := assetPersistence.NewLocationRepository()
	partnerRepository := userIdentityPersistence.NewPartnerRepository()
	accurateRepo, integrationRepo := integrationPersistence.NewAccurateRepository(), integrationPersistence.NewIntegrationRepository()
	assetRepo := assetPersistence.NewAssetRepository()
	assetInspectionRepo := assetPersistence.NewAssetInspectionRepository()
	partnerRepo := userIdentityPersistence.NewPartnerRepository()
	ticketRepo := taskPersistence.NewTicketRepository()
	approvalRepo := approvalPersistence.NewApprovalRepository()
	userIdentityRepo := userIdentityPersistence.NewUserRepository()
	assetStatusRequestRepo := assetPersistence.NewAssetStatusRequestRepository()
	bonusPenaltyRepo := assetPersistence.NewBonusPenaltyRepository()
	assetLinkedRepository := assetPersistence.NewAssetLinkedRepository()
	financeRepo := financePresistence.NewFinanceRepository()
	emailRepo := notificationPersistence.NewEmailRepository(email.GetEmailService())
	notificationRepo := notificationPersistence.NewAssetTyreRepository()

	// Use Cases
	financeUsecase := financeUseCase.NewFinanceUsecase(
		dbUsecase,
		financeRepo,
	)
	notificationUc := notificationUsecase.NewNotificationUsecase(
		dbUsecase,
		notificationRepo,
		emailRepo,
		userIdentityRepo,
	)

	approvalUsecase := approvalUsecase.NewApprovalUseCase(dbUsecase, approvalRepo, assetRepo, userIdentityRepo, assetStatusRequestRepo, bonusPenaltyRepo, assetLinkedRepository, financeUsecase, packageRepository, assetPersistence.NewAssetTyreRepository())
	attachmentUseCase := storageUsecase.NewAttachmentUseCase(dbUsecase, attachmentRepository, storageRepository)
	productUsecase := usecase.NewProductUseCase(dbUsecase, productRepository, inventoryRepository, attachmentUseCase, accurateRepo, integrationRepo)
	orderUsecase := usecase.NewOrderUseCase(
		dbUsecase,
		productRepository,
		inventoryRepository,
		orderRepository,
		accurateRepo,
		integrationRepo,
		ticketRepo,
		assetRepo,
		packageRepository,
		assetInspectionRepo,
		notificationUc,
		userIdentityRepo)
	packageUsecase := usecase.NewPackageUseCase(dbUsecase, productRepository, inventoryRepository, packageRepository, attachmentUseCase, assetRepo, partnerRepo, approvalUsecase)
	inventoryTransactionUsecase := usecase.NewInventoryUseCase(dbUsecase, inventoryTransactionRepository, inventoryRepository, locationRepository, *attachmentUseCase, partnerRepository, productRepository)

	// Handlers
	productHandler := handler.NewProductHandler(productUsecase)
	orderHandler := handler.NewOrderHandler(orderUsecase)
	packageHandler := handler.NewPackageHandler(packageUsecase)
	inventoryHandler := handler.NewInventoryHandler(inventoryTransactionUsecase)

	// Register Routes
	routers.RegisterProductRoutes(router, productHandler)
	routers.RegisterInventoryRoutes(router, inventoryHandler)
	routers.RegisterOrderRoutes(router, orderHandler)
	routers.RegisterPackageRoutes(router, packageHandler)

	return nil
}
