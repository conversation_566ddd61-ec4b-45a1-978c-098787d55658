package dtos

import "html/template"

type TyreWaveSource struct {
	NoOfInspectionPoint int     `json:"no_of_inspection_point"`
	RDT1                float64 `json:"rdt1"`
	RDT2                float64 `json:"rdt2"`
	RDT3                float64 `json:"rdt3"`
	RDT4                float64 `json:"rdt4"`
	OTD                 float64 `json:"otd"`
	ShowOTD             bool    `json:"show_otd"`
}

type TyreTread struct {
	Svg              template.HTML `json:"svg"`
	Color            string        `json:"color"`
	HeightPercentage float64       `json:"height_percentage"`
	Width            float64       `json:"width"`
	Value            float64       `json:"value"`
}

type TyreWaveSvgResponse struct {
	TyreWaveWidth       float64       `json:"tyre_wave_width"`
	TreadDepths         []float64     `json:"tread_depths"`
	TyreTreads          []TyreTread   `json:"tyre_treads"`
	BaseTread           template.HTML `json:"base_tread"`
	OTDLabel            string        `json:"otd_label"`
	ShowOTD             bool          `json:"show_otd"`
	InspectionPoints    []int         `json:"inspection_points"`
	NoOfInspectionPoint int           `json:"no_of_inspection_point"`
}
