package dtos

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/models"
	"assetfindr/pkg/common/commonmodel"
)

type CreateDigispectConfigReq struct {
	DigispectConfigID string

	BrandID    string `json:"brand_id"`
	BrandName  string `json:"brand_name"`
	TypeCode   string `json:"type_code"`
	ModelName  string `json:"model_name"`
	Size       string `json:"size"`
	StatusCode string `json:"status_code"`
	PatternID  string `json:"pattern_id"`
}

func (d *CreateDigispectConfigReq) Normalize() {
	if constants.MapDigispectType()[d.TypeCode] != "" {
		if d.TypeCode == constants.DIGISPECT_TYPE_VEHICLE {
			d.Size = ""
		} else {
			d.ModelName = ""
		}
	}
}

type CreateDigispectBrandReq struct {
	commonmodel.ListRequest
	BrandName  string   `json:"brand_name"`
	TypeCode   []string `json:"type_codes"`
	StatusCode string   `json:"status_code"`
}

func (c *CreateDigispectBrandReq) Normalize() {
	c.ListRequest.Normalize()

	if c.StatusCode == "" {
		c.StatusCode = constants.DIGISPECT_STATUS_ACTIVE
	}
}

type CreateDigispectBrandResp struct {
	BrandID    string   `json:"id"`
	BrandName  string   `json:"brand_name"`
	TypeCode   []string `json:"type_codes"`
	StatusCode string   `json:"status_code"`
}

type UpdateDigispectBrandReq struct {
	BrandID    string   `form:"id"`
	BrandName  string   `json:"brand_name"`
	TypeCode   []string `json:"type_codes"`
	StatusCode string   `json:"status_code"`
}

type DigispectBrandConfigListReq struct {
	commonmodel.ListRequest
	ShowDeleted bool     `form:"show_deleted"`
	LowerName   string   `form:"lower_name"`
	TypeCodes   []string `form:"type_codes"`
}

type DigispectBrandConfigListResp struct {
	ID         string   `json:"id"`
	BrandName  string   `json:"brand_name"`
	TypeCode   []string `json:"type"`
	StatusCode string   `json:"status_code"`
}

type DigispectConfigListReq struct {
	commonmodel.ListRequest
	ShowDeleted bool     `form:"show_deleted"`
	TypeCodes   []string `form:"type_codes"`
}

type DigispectConfigListResp struct {
	ID                 string                        `json:"id"`
	BrandDigispectID   string                        `json:"brand_digispect_id"`
	BrandDigiscpect    models.DigispectBrandConfig   `json:"brand_digispect"`
	PatternDigispectID string                        `json:"pattern_digispect_id"`
	PatternDigispect   models.DigispectPatternConfig `json:"pattern_digispect"`
	TypeCode           string                        `json:"type"`
	DigispectType      models.DigispectConfigType    `json:"digispect_type"`
	ModelConfig        string                        `json:"model"`
	SizeConfig         string                        `json:"size"`
	StatusCode         string                        `json:"status_code"`
}

type DigispectConfig struct {
	ID               string `json:"id"`
	BrandDigispectID string `json:"brand_digispect_id"`
	BrandName        string `json:"brand_name"`
	ModelConfig      string `json:"model"`
	SizeConfig       string `json:"size"`
	StatusCode       string `json:"status_code"`
}

func BuildDigispectConfigResp(digispectConfig models.DigispectConfig) *DigispectConfig {
	if digispectConfig.ID == "" {
		return nil
	}

	return &DigispectConfig{
		ID:               digispectConfig.ID,
		BrandDigispectID: digispectConfig.BrandDigispectID,
		BrandName:        digispectConfig.BrandDigiscpect.BrandName,
		ModelConfig:      digispectConfig.ModelConfig.String,
		SizeConfig:       digispectConfig.SizeConfig.String,
		StatusCode:       digispectConfig.StatusCode,
	}
}

type DigispectPatternConfigListReq struct {
	commonmodel.ListRequest
	ShowDeleted bool     `form:"show_deleted"`
	StatusCode  string   `form:"status_code"`
	StatusCodes []string `form:"status_codes"`
}

func (c *DigispectPatternConfigListReq) Normalize() {
	c.ListRequest.Normalize()
}

type DigispectPatternConfigListResp struct {
	ID          string `json:"id"`
	PatternName string `json:"pattern_name"`
	StatusCode  string `json:"status_code"`
}

type CreateDigispectPatternConfigReq struct {
	PatternName string `json:"pattern_name"`
	StatusCode  string `json:"status_code"`
}

func (c *CreateDigispectPatternConfigReq) Normalize() {
	if c.StatusCode == "" {
		c.StatusCode = constants.DIGISPECT_STATUS_ACTIVE
	}
}

type UpdateDigispectPatternConfigReq struct {
	PatternID   string `form:"id"`
	PatternName string `json:"pattern_name"`
	StatusCode  string `json:"status_code"`
}

type CreateDigispectPatternConfigResp struct {
	PatternID   string `json:"id"`
	PatternName string `json:"pattern_name"`
	StatusCode  string `json:"status_code"`
}
