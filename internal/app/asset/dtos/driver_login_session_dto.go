package dtos

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/pkg/common/commonmodel"

	"gopkg.in/guregu/null.v4"
)

type GetDriverLoginSessionListReq struct {
	commonmodel.ListRequest
	IsActive null.Bool `form:"is_active"`
}

func BuildDriverLoginSessionListResp(loginSessions []models.DriverLoginSession) []DriverLoginSessionResp {
	response := []DriverLoginSessionResp{}

	for _, loginSession := range loginSessions {
		response = append(response, BuildDriverLoginSessionResp(&loginSession))
	}

	return response
}
