package handler

import (
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/usecase"
	"assetfindr/internal/errorhandler"
	"net/http"

	"github.com/gin-gonic/gin"
)

type WeightBridgeHandler struct {
	weightBridgeUseCase *usecase.WeightBridgeUseCase
}

func NewWeightBridgeHandler(
	weightBridgeUseCase *usecase.WeightBridgeUseCase,
) *WeightBridgeHandler {
	return &WeightBridgeHandler{
		weightBridgeUseCase: weightBridgeUseCase,
	}
}

func (h *WeightBridgeHandler) GetWeightBridgeTickets(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.WeightBridgeTicketListReq{}
	err := c.Bind<PERSON>y(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.weightBridgeUseCase.GetWeightBridgeTickets(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.J<PERSON>(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *WeightBridgeHandler) GetWeightBridgeTicket(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	resp, err := h.weightBridgeUseCase.GetWeightBridgeTicket(ctx, id)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)

}

func (h *WeightBridgeHandler) GetWeightBridgeTicketSummary(c *gin.Context) {
	ctx := c.Request.Context()

	req := dtos.WeightBridgeTicketListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.weightBridgeUseCase.GetWeightBridgeTicketSummary(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *WeightBridgeHandler) CreateWeightBridge(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.CreateWeightBridgeTicket
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.weightBridgeUseCase.CreateWeightBridgeTicket(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *WeightBridgeHandler) UpdateWeightBridge(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")

	var req dtos.CreateWeightBridgeTicket
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.weightBridgeUseCase.UpdateWeightBridgeTicket(ctx, req, id)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *WeightBridgeHandler) DeleteWeightBridge(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")

	resp, err := h.weightBridgeUseCase.DeleteWeightBridgeTicket(ctx, id)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *WeightBridgeHandler) GetWeightBridgeTicketInboundSummaries(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.WeightBridgeTicketListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.weightBridgeUseCase.GetWeightBridgeTicketInboundSummaries(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *WeightBridgeHandler) GetWeightBridgeTicketOutboundSummaries(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.WeightBridgeTicketListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.weightBridgeUseCase.GetWeightBridgeTicketOutboundSummaries(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}
