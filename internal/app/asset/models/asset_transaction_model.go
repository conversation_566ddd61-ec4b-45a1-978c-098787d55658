package models

import (
	"assetfindr/pkg/common/commonmodel"
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type AssetTransaction struct {
	commonmodel.ModelV2
	AssetID               string                   `json:"asset_id"`
	PartnerID             string                   `json:"partner_id" gorm:"default:null"`
	PurchaseOrderDate     *null.Time               `json:"purchase_order_date"`
	PurchaseOrderNumber   null.String              `json:"purchase_order_number"`
	InvoiceDate           time.Time                `json:"invoice_date"`
	InvoiceNumber         null.String              `json:"invoice_number"`
	ServiceStartDate      time.Time                `json:"service_start_date" gorm:"default:null"`
	ServiceEndDate        time.Time                `json:"service_end_date" gorm:"default:null"`
	ReferenceNumber       string                   `json:"reference_number" gorm:"default:null"`
	StatusCode            string                   `json:"status_code"`
	TypeCode              string                   `json:"type_code"`
	Cost                  int                      `json:"cost"`
	AssignedToUserID      string                   `json:"assigned_to_user_id"`
	TaxCost               int                      `gorm:"type:bigint" json:"tax_cost"`
	DiscountAmount        int                      `gorm:"type:bigint" json:"discount_amount"`
	OtherCost             int                      `gorm:"type:bigint" json:"other_cost"`
	SubTotal              int                      `gorm:"type:bigint" json:"sub_total"`
	Notes                 null.String              `json:"notes"`
	Status                AssetTransactionStatus   `json:"status"`
	Type                  AssetTransactionType     `json:"type"`
	AssetTransactionItems []AssetTransactionItem   `gorm:"foreignKey:AssetTransactionID;references:ID"`
	CategoryCode          string                   `json:"category_code" gorm:"default:null"`
	Category              AssetTransactionCategory `json:"category"`
	// PaymentMethodCode     string                   `json:"payment_method_code" gorm:"default:null"`
	// PaymentMethod         AssetTransactionMethod   `json:"payment_method"`
	Location string  `json:"location" gorm:"default:null"`
	Odometer float64 `json:"odometer" gorm:"default:null"`
}

func (at AssetTransaction) TableName() string {
	return "ams_asset_transactions"
}

func (at *AssetTransaction) BeforeCreate(db *gorm.DB) error {
	at.SetUUID("atr")
	at.ModelV2.BeforeCreate(db)
	return nil
}

func (at *AssetTransaction) BeforeUpdate(db *gorm.DB) error {
	at.ModelV2.BeforeUpdate(db)
	return nil
}

type AssetTransactionStatus struct {
	Code        string `gorm:"primaryKey"`
	Label       string
	Description string
}

func (ps AssetTransactionStatus) TableName() string {
	return "ams_ASSET_TRANSACTION_STATUSES"
}

type AssetTransactionType struct {
	Code        string `gorm:"primaryKey"`
	Label       string
	Description string
}

func (ps AssetTransactionType) TableName() string {
	return "ams_ASSET_TRANSACTION_TYPES"
}

type AssetTransactionWhere struct {
	ID           string
	AssetID      string
	ClientID     string
	Type         string
	PartnerID    string
	IsNearExpiry bool
}

type AssetTransactionCondition struct {
	Where   AssetTransactionWhere
	Columns []string
	Preload AssetTransactionPreload
}

type AssetTransactionPreload struct {
	Status                bool
	Type                  bool
	Category              bool
	PaymentMethod         bool
	AssetTransactionItems bool
}

type GetAssetTransactionListParam struct {
	commonmodel.ListRequest
	Cond AssetTransactionCondition
}
