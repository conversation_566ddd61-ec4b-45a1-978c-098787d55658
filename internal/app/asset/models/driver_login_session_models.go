package models

import (
	"assetfindr/pkg/common/commonmodel"
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type DriverLoginSession struct {
	commonmodel.ModelV2
	AssetID       string    `json:"asset_id" gorm:"type:varchar(40);not null"`
	UserID        string    `json:"user_id" gorm:"type:varchar(40);not null"`
	UserFullName  string    `json:"user_full_name" gorm:"type:varchar(100)"`
	LoggedInTime  time.Time `json:"logged_in_time" gorm:"not null"`
	LoggedOutTime null.Time `json:"logged_out_time" gorm:"default:null"`

	StartVehicleHm    null.Float `json:"start_vehicle_hm" gorm:"default:null"`
	StartVehicleKm    null.Float `json:"start_vehicle_km" gorm:"default:null"`
	StartFuelConsumed null.Float `json:"start_fuel_consumed" gorm:"default:null"`

	EndVehicleHm    null.Float `json:"end_vehicle_hm" gorm:"default:null"`
	EndVehicleKm    null.Float `json:"end_vehicle_km" gorm:"default:null"`
	EndFuelConsumed null.Float `json:"end_fuel_consumed" gorm:"default:null"`

	VehicleHm    null.Float `json:"vehicle_hm" gorm:"default:null"`
	VehicleKm    null.Float `json:"vehicle_km" gorm:"default:null"`
	FuelConsumed null.Float `json:"fuel_consumed" gorm:"default:null"`

	VehicleKmPerHm    null.Float `json:"vehicle_km_per_hm" gorm:"default:null"`
	FuelConsumedPerKm null.Float `json:"fuel_consumed_per_km" gorm:"default:null"`

	TotalNetWeight null.Float `json:"total_net_weight" gorm:"default:null"`
	TotalTrip      null.Int   `json:"total_trip" gorm:"default:null"`

	// Relationships
	Asset Asset `json:"asset" gorm:"foreignKey:AssetID;references:ID"`
}

func (dls *DriverLoginSession) TableName() string {
	return "ams_driver_login_sessions"
}

func (dls *DriverLoginSession) BeforeCreate(db *gorm.DB) error {
	dls.SetUUID("dls")
	dls.ModelV2.BeforeCreate(db)
	return nil
}

func (dls *DriverLoginSession) BeforeUpdate(db *gorm.DB) error {
	dls.ModelV2.BeforeUpdate(db)
	return nil
}

type DriverLoginSessionWhere struct {
	ID         string
	AssetID    string
	NotAssetID string
	UserID     string
	ClientID   string
	IsActive   null.Bool // For filtering active sessions (LoggedOutTime is null)
}

type DriverLoginSessionPreload struct {
	Asset bool
}

type DriverLoginSessionCondition struct {
	Where   DriverLoginSessionWhere
	Preload DriverLoginSessionPreload
	Columns []string
}

type GetDriverLoginSessionListParam struct {
	commonmodel.ListRequest
	Cond DriverLoginSessionCondition
}
