package models

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/pkg/common/commonmodel"
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type Hauling struct {
	commonmodel.ModelV2
	AssetID             string      `json:"asset_id"`
	OperatorUserID      string      `json:"operator_user_id" gorm:"default:null"`
	HaulStatusCode      string      `json:"haul_status_code"`
	HaulActivityCode    string      `json:"haul_activity_code"`
	HaulSubActivityCode null.String `json:"haul_sub_activity_code" gorm:"default:null"`
	StartTime           time.Time   `json:"start_time"`
	EndTime             null.Time   `json:"end_time"`

	// Relationships
	HaulStatus      HaulStatus       `json:"haul_status" gorm:"foreignKey:HaulStatusCode;references:Code"`
	HaulActivity    HaulActivity     `json:"haul_sub_status" gorm:"foreignKey:HaulActivityCode;references:Code"`
	HaulSubActivity *HaulSubActivity `json:"haul_sub_activity" gorm:"foreignKey:HaulSubActivityCode;references:Code"`

	CurrentDriverLoginSession *DriverLoginSession   `json:"current_driver_login_session" gorm:"foreignKey:AssetID;references:AssetID"`
	Asset                     *Asset                `json:"asset" gorm:"foreignKey:AssetID;references:ID"`
	LoadStatus                *HaulLoadStatus       `json:"load_status" gorm:"foreignKey:AssetID;references:AssetID"`
	DispacthLocation          *HaulDispatchLocation `json:"dispatch_location" gorm:"foreignKey:AssetID;references:AssetID"`
}

func (h *Hauling) TableName() string {
	return "ams_haulings"
}

func (h *Hauling) BeforeCreate(db *gorm.DB) error {
	h.SetUUID("hul")
	h.ModelV2.BeforeCreate(db)
	return nil
}

func (h *Hauling) BeforeUpdate(db *gorm.DB) error {
	h.ModelV2.BeforeUpdate(db)
	return nil
}

func (h Hauling) IsInReadyStatus() bool {
	return h.HaulStatusCode == constants.HAUL_STATUS_CODE_READY
}

func (h Hauling) IsInChangeShiftActivity() bool {
	return h.HaulStatusCode == constants.HAUL_STATUS_CODE_DELAY &&
		h.HaulActivityCode == constants.HAUL_ACTIVITY_CODE_CHANGE_SHIFT
}

func (h Hauling) IsInLoadingActivity() bool {
	return h.HaulActivityCode == constants.HAUL_ACTIVITY_CODE_LOADING
}

func (h Hauling) IsInDumpingActivity() bool {
	return h.HaulActivityCode == constants.HAUL_ACTIVITY_CODE_DUMPING
}

type HaulingWhere struct {
	ID                  string
	AssetID             string
	AssetIDs            []string
	OperatorUserID      string
	HaulStatusCode      string
	HaulActivityCode    string
	HaulSubActivityCode string
	ClientID            string
	IsActive            null.Bool // For filtering ongoing haulings (EndTime is null)

	HaulStatusCodes       []string
	HaulActivityCodes     []string
	HaulSubActivityCodes  []string
	IsLoad                null.Bool
	IsOver12HLoginSession bool
}

type HaulingPreload struct {
	HaulStatus           bool
	HaulActivity         bool
	HaulActivityWithNext bool
	HaulActivityWithPrev bool
	HaulSubActivity      bool

	CurrentDriverLoginSession bool
	Asset                     bool
	LoadStatus                bool
	DispacthLocation          bool
}

type HaulingCondition struct {
	Where   HaulingWhere
	Columns []string
	Preload HaulingPreload
}

type GetHaulingStatusBoardParam struct {
	SearchKeyword string
	Cond          HaulingCondition
}

type HaulingVehicleLoadStatus struct {
	AssetID string
	IsLoad  bool
}
