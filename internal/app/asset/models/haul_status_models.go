package models

import (
	"gopkg.in/guregu/null.v4"
)

type HaulStatus struct {
	Code        string `json:"code" gorm:"primaryKey;type:varchar(20)"`
	Label       string `json:"label" gorm:"type:varchar(20);not null"`
	Description string `json:"description" gorm:"type:varchar(50);not null"`
	Rank        int    `json:"rank"`
}

func (hs *HaulStatus) TableName() string {
	return "ams_HAUL_STATUSES"
}

type HaulActivity struct {
	Code           string    `json:"code" gorm:"primaryKey;type:varchar(50)"`
	Label          string    `json:"label" gorm:"type:varchar(100);not null"`
	Description    string    `json:"description" gorm:"type:varchar(100);not null"`
	HaulStatusCode string    `json:"haul_status_code" gorm:"type:varchar(20);not null"`
	Rank           int       `json:"rank" gorm:"type:smallint"`
	IsLoad         null.Bool `json:"is_load"`

	// Relationships
	HaulStatus        HaulStatus         `json:"haul_status" gorm:"foreignKey:HaulStatusCode;references:Code"`
	NextActivityCycle *HaulActivityCycle `json:"next_activities,omitempty" gorm:"foreignKey:PrevCode;references:Code"`
	PrevActivityCycle *HaulActivityCycle `json:"prev_activities,omitempty" gorm:"foreignKey:NextCode;references:Code"`
	HaulSubActivities []HaulSubActivity  `json:"haul_sub_activities,omitempty" gorm:"foreignKey:MainActivityCode;references:Code"`
}

func (hss *HaulActivity) TableName() string {
	return "ams_HAUL_ACTIVITIES"
}

type HaulSubActivity struct {
	Code             string   `json:"code" gorm:"primaryKey;type:varchar(50)"`
	Label            string   `json:"label" gorm:"type:varchar(100);not null"`
	Description      string   `json:"description" gorm:"type:varchar(100);not null"`
	MainActivityCode string   `json:"main_activity_code" gorm:"type:varchar(20);not null"`
	Rank             null.Int `json:"rank" gorm:"type:smallint"`

	// Relationships
	MainActivity HaulActivity `json:"main_activity" gorm:"foreignKey:MainActivityCode;references:Code"`
}

func (hsa *HaulSubActivity) TableName() string {
	return "ams_HAUL_SUB_ACTIVITIES"
}

type HaulActivityCycle struct {
	PrevCode string `json:"prev_code" gorm:"primaryKey;type:varchar(50)"`
	NextCode string `json:"next_code" gorm:"primaryKey;type:varchar(50)"`

	// Relationships
	PrevActivity HaulActivity `json:"previous_activity" gorm:"foreignKey:PrevCode;references:Code"`
	NextActivity HaulActivity `json:"next_activity" gorm:"foreignKey:NextCode;references:Code"`
}

func (hac *HaulActivityCycle) TableName() string {
	return "ams_HAUL_ACTIVITY_CYCLES"
}

type HaulStatusWhere struct {
	Code  string
	Codes []string
}

type HaulActivityWhere struct {
	Code               string
	Codes              []string
	HaulStatusCode     string
	RankLargerThanZero bool
}

type HaulSubActivityWhere struct {
	Code             string
	Codes            []string
	MainActivityCode string
}

type HaulStatusPreload struct {
	HaulActivities bool
}

type HaulActivityPreload struct {
	HaulStatus        bool
	HaulSubActivities bool
}

type HaulSubActivityPreload struct {
	MainActivity bool
}

type HaulStatusCondition struct {
	Where   HaulStatusWhere
	Columns []string
	Preload HaulStatusPreload
}

type HaulActivityCondition struct {
	Where   HaulActivityWhere
	Columns []string
	Preload HaulActivityPreload
}

type HaulSubActivityCondition struct {
	Where   HaulSubActivityWhere
	Preload HaulSubActivityPreload
	Columns []string
}
