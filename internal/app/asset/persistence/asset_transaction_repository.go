package persistence

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"context"
	"time"

	"gorm.io/gorm"
)

type assetTransactionRepository struct{}

func NewAssetTransactionRepository() repository.AssetTransactionRepository {
	return &assetTransactionRepository{}
}

func (r *assetTransactionRepository) CreateAssetTransaction(ctx context.Context, dB database.DBI, assetTransaction *models.AssetTransaction) error {
	return dB.GetTx().Create(assetTransaction).Error
}

func enrichAssetTransactionQueryWithWhere(query *gorm.DB, where models.AssetTransactionWhere) {
	if where.ID != "" {
		query.Where("id = ?", where.ID)
	} // ID

	if where.AssetID != "" {
		query.Where("asset_id = ?", where.AssetID)
	} // ID

	if where.Type != "" {
		query.Where("type_code = ?", where.Type)
	} // ID

	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	} // ClientID

	if where.PartnerID != "" {
		query.Where("partner_id = ?", where.PartnerID)
	} // PartnerID

	if where.IsNearExpiry {
		today := time.Now().UTC().Truncate(24 * time.Hour)
		aWeekFromNow := today.AddDate(0, 0, 7)
		aMonthFromNow := today.AddDate(0, 0, 30)

		query.Where("DATE(service_end_date) = ? OR DATE(service_end_date) = ? OR DATE(service_end_date) = ?", today, aWeekFromNow, aMonthFromNow)
	} // Near Expiry Asset Transactions
}

func enrichAssetTransactionQueryWithPreload(query *gorm.DB, preload models.AssetTransactionPreload) {
	if preload.Status {
		query.Preload("Status")
	} // preload.Status

	if preload.Type {
		query.Preload("Type")
	} // preload.Type

	if preload.AssetTransactionItems {
		query.Preload("AssetTransactionItems")
	} // preload.AssetTransactionItems

	if preload.Category {
		query.Preload("Category")
	}
	if preload.PaymentMethod {
		query.Preload("PaymentMethod")
	}
}

func (r *assetTransactionRepository) GetAssetTransaction(ctx context.Context, dB database.DBI, cond models.AssetTransactionCondition) (*models.AssetTransaction, error) {
	assetTransaction := models.AssetTransaction{}
	query := dB.GetOrm().Model(&assetTransaction)

	enrichAssetTransactionQueryWithWhere(query, cond.Where)
	enrichAssetTransactionQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.First(&assetTransaction).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("asset transaction")
		}

		return nil, err
	}

	return &assetTransaction, nil
}

func (r *assetTransactionRepository) GetAssetTransactionList(ctx context.Context, dB database.DBI, param models.GetAssetTransactionListParam) (int, []models.AssetTransaction, error) {
	var totalRecords int64
	assetTransactions := []models.AssetTransaction{}
	query := dB.GetTx().Model(&assetTransactions)
	enrichAssetTransactionQueryWithWhere(query, param.Cond.Where)
	enrichAssetTransactionQueryWithPreload(query, param.Cond.Preload)

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().Where("LOWER(reference_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(invoice_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(purchase_order_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("updated_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&assetTransactions).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), assetTransactions, nil

}

func (r *assetTransactionRepository) UpdateAssetTransaction(ctx context.Context, dB database.DBI, id string, assetTransaction *models.AssetTransaction) error {
	return dB.GetTx().
		Model(&models.AssetTransaction{}).
		Where("id = ?", id).
		Updates(assetTransaction).
		Error
}

func (r *assetTransactionRepository) UpdateAssetTransactionItem(ctx context.Context, dB database.DBI, id string, assetTransactionItem *models.AssetTransactionItem) error {
	return dB.GetTx().
		Model(&models.AssetTransactionItem{}).
		Where("id = ?", id).
		Updates(assetTransactionItem).
		Error
}

func (r *assetTransactionRepository) DeleteAssetTransactionItemByIDs(ctx context.Context, dB database.DBI, ids []string) error {
	if len(ids) == 0 {
		return nil
	}

	return dB.GetTx().Delete(&models.AssetTransactionItem{}, ids).Error
}

func (r *assetTransactionRepository) CreateAssetTransactionItems(ctx context.Context, dB database.DBI, assetTransactionItems []models.AssetTransactionItem) error {
	if len(assetTransactionItems) == 0 {
		return nil
	}

	return dB.GetTx().Create(&assetTransactionItems).Error
}

func (r *assetTransactionRepository) GetAssetTransactionCategories(ctx context.Context, dB database.DBI) ([]models.AssetTransactionCategory, error) {
	atCategories := []models.AssetTransactionCategory{}
	err := dB.GetOrm().Find(&atCategories).Error
	if err != nil {
		return nil, err
	}

	return atCategories, nil
}

func (r *assetTransactionRepository) GetAssetTransactionPaymentMethods(ctx context.Context, dB database.DBI) ([]models.AssetTransactionMethod, error) {
	atPaymentMethods := []models.AssetTransactionMethod{}
	err := dB.GetOrm().Find(&atPaymentMethods).Error
	if err != nil {
		return nil, err
	}

	return atPaymentMethods, nil
}

func (r *assetTransactionRepository) GetAssetTransactions(ctx context.Context, dB database.DBI, param models.GetAssetTransactionListParam) ([]models.AssetTransaction, error) {
	assetTransactions := []models.AssetTransaction{}
	query := dB.GetTx().Model(&assetTransactions)
	enrichAssetTransactionQueryWithWhere(query, param.Cond.Where)
	enrichAssetTransactionQueryWithPreload(query, param.Cond.Preload)

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().Where("LOWER(reference_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(invoice_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(purchase_order_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	query.Order("updated_at DESC")
	err := query.Find(&assetTransactions).Error
	if err != nil {
		return nil, err
	}

	return assetTransactions, nil
}
