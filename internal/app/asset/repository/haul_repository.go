package repository

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/infrastructure/database"
	"context"
)

type HaulRepository interface {
	GetDriverLoginSession(ctx context.Context, dB database.DBI, cond models.DriverLoginSessionCondition) (*models.DriverLoginSession, error)
	GetDriverLoginSessions(ctx context.Context, dB database.DBI, cond models.DriverLoginSessionCondition) ([]models.DriverLoginSession, error)
	GetDriverLoginSessionList(ctx context.Context, dB database.DBI, param models.GetDriverLoginSessionListParam) (int, []models.DriverLoginSession, error)
	CreateDriverLoginSession(ctx context.Context, dB database.DBI, driverLoginSession *models.DriverLoginSession) error
	UpdateDriverLoginSession(ctx context.Context, dB database.DBI, id string, driverLoginSession *models.DriverLoginSession) error

	GetHaulStatuses(ctx context.Context, dB database.DBI, cond models.HaulStatusCondition) ([]models.HaulStatus, error)
	GetHaulActivities(ctx context.Context, dB database.DBI, cond models.HaulActivityCondition) ([]models.HaulActivity, error)
	GetHaulActivity(ctx context.Context, dB database.DBI, cond models.HaulActivityCondition) (*models.HaulActivity, error)
	GetHaulSubActivities(ctx context.Context, dB database.DBI, cond models.HaulSubActivityCondition) ([]models.HaulSubActivity, error)
	GetHaulSubActivity(ctx context.Context, dB database.DBI, cond models.HaulSubActivityCondition) (*models.HaulSubActivity, error)

	CreateHauling(ctx context.Context, dB database.DBI, hauling *models.Hauling) error
	UpdateHauling(ctx context.Context, dB database.DBI, id string, hauling *models.Hauling) error
	GetHauling(ctx context.Context, dB database.DBI, cond models.HaulingCondition) (*models.Hauling, error)

	GetHaulingStatusBoard(ctx context.Context, dB database.DBI, param models.GetHaulingStatusBoardParam) ([]models.Hauling, error)

	UpsertHaulDispatchLocation(ctx context.Context, dB database.DBI, haulDispatchLocation *models.HaulDispatchLocation) error
	GetHaulDispatchLocation(ctx context.Context, dB database.DBI, cond models.HaulDispatchLocationCondition) (*models.HaulDispatchLocation, error)
	ClearHaulDispatchLoadLocationTrip(ctx context.Context, dB database.DBI, assetID string) error
	ClearHaulDispatchDumpLocationTrip(ctx context.Context, dB database.DBI, assetID string) error

	InitHaulLoadStatus(ctx context.Context, dB database.DBI, haulLoadStatus *models.HaulLoadStatus) error
}
