package routers

import (
	"assetfindr/internal/app/asset/handler"
	"assetfindr/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterWeightBridgeRoutes(route *gin.Engine, weightBridgeHandler *handler.WeightBridgeHandler) *gin.Engine {
	weightBridgeTicketRoutes := route.Group("/v1/weight-bridge-tickets", middleware.TokenValidationMiddleware())
	{
		weightBridgeTicketRoutes.GET("", weightBridgeHandler.GetWeightBridgeTickets) // here
		weightBridgeTicketRoutes.GET("/:id", weightBridgeHandler.GetWeightBridgeTicket)
		weightBridgeTicketRoutes.POST("", weightBridgeHandler.CreateWeightBridge)
		weightBridgeTicketRoutes.PUT("/:id", weightBridgeHandler.UpdateWeightBridge)
		weightBridgeTicketRoutes.DELETE("/:id", weightBridgeHandler.DeleteWeightBridge)
		weightBridgeTicketRoutes.GET("/summary", weightBridgeHandler.GetWeightBridgeTicketSummary)                      // here
		weightBridgeTicketRoutes.GET("/inbound-summaries", weightBridgeHandler.GetWeightBridgeTicketInboundSummaries)   // here
		weightBridgeTicketRoutes.GET("/outbound-summaries", weightBridgeHandler.GetWeightBridgeTicketOutboundSummaries) // here
	}

	return route
}
