package usecase

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/models"
	contentConstants "assetfindr/internal/app/content/constants"
	contentDtos "assetfindr/internal/app/content/dtos"
	contentModel "assetfindr/internal/app/content/models"
	notifConstants "assetfindr/internal/app/notification/constants"
	notificationDtos "assetfindr/internal/app/notification/dtos"
	storageConstants "assetfindr/internal/app/storage/constants"
	storageDtos "assetfindr/internal/app/storage/dtos"
	userIdentityModel "assetfindr/internal/app/user-identity/models"
	userModels "assetfindr/internal/app/user-identity/models"
	"assetfindr/internal/errorhandler"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/pgtypehelpers"
	"assetfindr/pkg/common/helpers/tmplhelpers"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"html/template"
	"os"
	"time"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
)

// use AssetAssignmentUseCase here

func (uc *AssetAssignmentUseCase) notifyAssetHandoverToTargetUser(ctx context.Context, notifType string, targetUser *userModels.User, beforeUser *userModels.User, clientID string, assetID string, assetHandover *models.AssetHandoverRequest) {
	// GET CLIENT DATA
	client, err := uc.UserRepository.GetClient(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
		Where: userIdentityModel.ClientWhere{
			ID: clientID,
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get client for email %v", err)
		return
	}

	// GET ASSET DATA
	asset, err := uc.AssetRepository.GetAssetByID(ctx, uc.DB.DB(), assetID)
	if err != nil {
		commonlogger.Warnf("Error in getting assets by id", err)
		return
	}

	// CONSTRUCT NOTIFICATION
	assetName := ""
	if asset.ReferenceNumber == "" {
		assetName = fmt.Sprintf("%s - %s", asset.Name, asset.SerialNumber)
	} else {
		assetName = fmt.Sprintf("%s - %s", asset.Name, asset.ReferenceNumber)
	}

	subject := ""
	notifUserID := ""
	if notifType == constants.ASSET_HANDOVER_NOTIF_TYPE_REQUEST {
		subject = fmt.Sprintf("Action Required: Handover Request for %s", assetName)
		notifUserID = targetUser.ID
	}
	if notifType == constants.ASSET_HANDOVER_NOTIF_TYPE_ACCEPT {
		subject = fmt.Sprintf("Handover Accepted for %s", assetName)
		notifUserID = beforeUser.ID
	}
	if notifType == constants.ASSET_HANDOVER_NOTIF_TYPE_DECLINE {
		subject = fmt.Sprintf("Handover Declined for %s", assetName)
		notifUserID = beforeUser.ID
	}

	href := uc.NotificationUsecase.GenerateTargetURL(ctx, client.ClientAlias, notifConstants.DESTINATION_TYPE_ASSET_HANDOVER, assetHandover.ID)
	handoverDate := assetHandover.CreatedAt.Format("02 January 2006")

	templateBod := tmplhelpers.AssetHandoverNotificationBody{
		Subject:        subject,
		AssetName:      assetName,
		TargetUserName: targetUser.GetName(),
		BeforeUserName: beforeUser.GetName(),
		RedirectLink:   template.URL(href),
		HandoverDate:   handoverDate,
		NotifType:      notifType,
	}
	err = tmplhelpers.ConstructAssetHandoverNotification(&templateBod)
	if err != nil {
		commonlogger.Warnf("error generate notify template body", err)
		return
	}

	notifItem := notificationDtos.CreateNotificationItem{
		SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_ASSET_HANDOVER,
		SourceReferenceID: assetHandover.ID,
		TargetReferenceID: assetID,
		TargetURL:         href,
		MessageHeader:     templateBod.TitleEmail,
		MessageBody:       templateBod.BodyEmail,
		MessageFirebase: notificationDtos.MessageFirebase{
			Title: templateBod.TitleFirebase,
			Body:  templateBod.BodyFirebase,
		},
		ClientID:        clientID,
		TypeCode:        "",
		ContentTypeCode: "",
		ReferenceCode:   notifConstants.NOTIF_REF_ASSET_HANDOVER,
		ReferenceValue:  assetHandover.ID,
		UserID:          notifUserID,
	}

	itemNotifications := []notificationDtos.CreateNotificationItem{}
	itemNotifications = append(itemNotifications, notifItem)

	uc.NotificationUsecase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
		Items:           itemNotifications,
		SendToEmail:     true,
		SendToPushNotif: true,
	})
}

func (uc *AssetAssignmentUseCase) UpsertAssetHandoverTemplate(ctx context.Context, req dtos.UpsertAssetHandoverTemplateReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	asset, err := uc.AssetRepository.GetAsset(ctx, uc.DB.DB(), models.AssetCondition{
		Where: models.AssetWhere{
			ID:       req.AssetID,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	if asset.HandoverFormTemplateID.String != req.HandoverFormTemplateID ||
		asset.HandoverNeedInspection.Bool != req.HandoverNeedInspection {
		err := uc.AssetRepository.UpdateAsset(ctx, uc.DB.DB(), &models.Asset{
			ModelV2: commonmodel.ModelV2{
				ID: req.AssetID,
			},
			HandoverFormTemplateID: null.StringFrom(req.HandoverFormTemplateID),
			HandoverNeedInspection: null.BoolFrom(req.HandoverNeedInspection),
		})
		if err != nil {
			return nil, err
		}
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Sucess",
		ReferenceID: "",
		Data:        nil,
	}, nil
}

func (uc *AssetAssignmentUseCase) CreateAssetHandoverRequest(ctx context.Context, req dtos.CreateAssetHandoverRequestReq) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	targetUser, err := uc.UserRepository.GetUser(ctx, uc.DB.DB(), userModels.UserCondition{
		Where: userModels.UserWhere{
			ID:       req.TargetAssignedUserID,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	beforeUser, err := uc.UserRepository.GetUser(ctx, uc.DB.DB(), userModels.UserCondition{
		Where: userModels.UserWhere{
			ID:       req.BeforeAssignedUserID,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	// currAssetAssignment, err := uc.AssetAssignmentRepository.GetAssetAssignment(ctx, uc.DB.DB(), models.AssetAssignmentCondition{
	// 	Where: models.AssetAssignmentWhere{
	// 		AssetID:  req.AssetID,
	// 		ClientID: claim.GetLoggedInClientID(),
	// 		Assigned: true,
	// 	},
	// })
	// if err != nil && !errorhandler.IsErrNotFound(err) {
	// 	return nil, err
	// }

	ahr := &models.AssetHandoverRequest{
		AssetID:              req.AssetID,
		BeforeAssignedUserID: req.BeforeAssignedUserID,
		TargetAssignedUserID: req.TargetAssignedUserID,
		FormTemplateID:       req.FormTemplateID,
		NeedInspection:       req.NeedInspection,
		StatusCode:           constants.ASSET_HANDOVER_REQUEST_STATUS_CODE_PENDING,
	}
	err = uc.AssetAssignmentRepository.CreateAssetHandoverRequest(ctx, uc.DB.WithCtx(ctx).DB(), ahr)
	if err != nil {
		return nil, err
	}

	_, err = uc.attachmentUsecase.CreateAttachmentsPhotosV2(ctx, storageDtos.UpsertAttachmentReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_ASSET_HANDOVER,
		SourceReferenceID: ahr.ID,
		TargetReferenceID: "",
		ClientID:          claim.GetLoggedInClientID(),
		Photos:            req.Photos,
	})
	if err != nil {
		return nil, err
	}

	uc.notifyAssetHandoverToTargetUser(ctx, constants.ASSET_HANDOVER_NOTIF_TYPE_REQUEST, targetUser, beforeUser, claim.GetLoggedInClientID(), req.AssetID, ahr)

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Sucess",
		ReferenceID: ahr.ID,
		Data:        nil,
	}, nil
}

func (uc *AssetAssignmentUseCase) UpdateAssetHandoverRequest(ctx context.Context, id string, req dtos.UpdateAssetHandoverRequestReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	assetHandoverRequest, err := uc.AssetAssignmentRepository.GetAssetHandoverRequest(ctx, uc.DB.DB(), models.AssetHandoverRequestCondition{
		Where: models.AssetHandoverRequestWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	if assetHandoverRequest.StatusCode == constants.ASSET_HANDOVER_REQUEST_STATUS_CODE_REJECTED {
		return nil, errorhandler.ErrBadRequest("asset handover already rejected")
	}

	if assetHandoverRequest.StatusCode == constants.ASSET_HANDOVER_REQUEST_STATUS_CODE_ACCEPTED {
		return nil, errorhandler.ErrBadRequest("asset handover already accepted")
	}

	// If Target Changed, validate user
	if assetHandoverRequest.TargetAssignedUserID != req.TargetAssignedUserID {
		_, err = uc.UserRepository.GetUser(ctx, uc.DB.DB(), userModels.UserCondition{
			Where: userModels.UserWhere{
				ID:       req.TargetAssignedUserID,
				ClientID: claim.GetLoggedInClientID(),
			},
		})
		if err != nil {
			return nil, err
		}
	}

	newAhr := &models.AssetHandoverRequest{
		BeforeAssignedUserID: req.BeforeAssignedUserID,
		TargetAssignedUserID: req.TargetAssignedUserID,
		StatusCode:           req.StatusCode,
		FormTemplateID:       req.FormTemplateID,
		NeedInspection:       req.NeedInspection,
	}

	// If Decline, Then Add Reason
	if req.StatusCode == constants.ASSET_HANDOVER_REQUEST_STATUS_CODE_REJECTED {
		newAhr.RejectReason = req.RejectReason
	}

	if req.StatusCode == constants.ASSET_HANDOVER_REQUEST_STATUS_CODE_ACCEPTED {
		newAhr.ConfirmDate = time.Now().UTC()
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	err = uc.AssetAssignmentRepository.UpdateAssetHandoverRequest(ctx, tx.DB(), id, newAhr)
	if err != nil {
		return nil, err
	}

	if req.StatusCode == constants.ASSET_HANDOVER_REQUEST_STATUS_CODE_ACCEPTED {
		err := uc.ReassignAssetAssignmentV2(ctx, tx,
			assetHandoverRequest.AssetID,
			assetHandoverRequest.TargetAssignedUserID,
			assetHandoverRequest.CreatedBy,
		)
		if err != nil {
			return nil, err
		}
	}

	_, err = uc.attachmentUsecase.UpdateAttachmentPhotos(ctx, storageDtos.UpsertAttachmentReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_ASSET_HANDOVER,
		SourceReferenceID: assetHandoverRequest.ID,
		TargetReferenceID: "",
		ClientID:          claim.GetLoggedInClientID(),
		Photos:            req.Photos,
	})
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	// SEND NOTIFICATION
	assetHandoverRequest, err = uc.AssetAssignmentRepository.GetAssetHandoverRequest(ctx, uc.DB.DB(), models.AssetHandoverRequestCondition{
		Where: models.AssetHandoverRequestWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})

	if err != nil {
		return nil, err
	}

	handoverNotifType := ""
	if assetHandoverRequest.StatusCode == constants.ASSET_HANDOVER_REQUEST_STATUS_CODE_REJECTED {
		handoverNotifType = constants.ASSET_HANDOVER_NOTIF_TYPE_DECLINE
	}
	if assetHandoverRequest.StatusCode == constants.ASSET_HANDOVER_REQUEST_STATUS_CODE_ACCEPTED {
		handoverNotifType = constants.ASSET_HANDOVER_NOTIF_TYPE_ACCEPT
	}

	targetUser, err := uc.UserRepository.GetUser(ctx, uc.DB.DB(), userModels.UserCondition{
		Where: userModels.UserWhere{
			ID:       assetHandoverRequest.TargetAssignedUserID,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get target assigned user for asset handover notification %v", err)
	}

	beforeUser, err := uc.UserRepository.GetUser(ctx, uc.DB.DB(), userModels.UserCondition{
		Where: userModels.UserWhere{
			ID:       assetHandoverRequest.BeforeAssignedUserID,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get before assigned user for asset handover notification %v", err)
	}

	if handoverNotifType == constants.ASSET_HANDOVER_NOTIF_TYPE_ACCEPT || handoverNotifType == constants.ASSET_HANDOVER_NOTIF_TYPE_DECLINE {
		newAhr = assetHandoverRequest
	}

	uc.notifyAssetHandoverToTargetUser(ctx, handoverNotifType, targetUser, beforeUser, claim.GetLoggedInClientID(), assetHandoverRequest.AssetID, newAhr)

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Sucess",
		ReferenceID: assetHandoverRequest.ID,
		Data:        nil,
	}, nil
}

func (uc *AssetAssignmentUseCase) GetAssetHandoverRequestList(ctx context.Context, req dtos.GetAssetHandoverRequestListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	total, assetHandoverRequests, err := uc.AssetAssignmentRepository.GetAssetHandoverRequestList(ctx, uc.DB.DB(), models.GetAssetHandoverRequestListParam{
		ListRequest: req.ListRequest,
		Cond: models.AssetHandoverRequestCondition{
			Where: models.AssetHandoverRequestWhere{
				ClientID: claim.GetLoggedInClientID(),
			},
			Preload: models.AssetHandoverRequestPreload{
				Status: true,
				AssetPreload: models.AssetPreload{
					Location:               true,
					AssetCategory:          true,
					SubCategory:            true,
					CustomAssetSubCategory: true,
					CustomAssetCategory:    true,
				},
			},
		},
	})
	if err != nil {
		return nil, err
	}

	if len(assetHandoverRequests) == 0 {
		return &commonmodel.ListResponse{
			TotalRecords: total,
			PageSize:     req.PageSize,
			PageNo:       req.PageNo,
			Data:         nil,
		}, nil
	}

	mapUserName := map[string]string{}
	userIDs := []string{}
	for i := range assetHandoverRequests {
		if assetHandoverRequests[i].BeforeAssignedUserID != "" {
			userIDs = append(userIDs, assetHandoverRequests[i].BeforeAssignedUserID)
		}

		if assetHandoverRequests[i].TargetAssignedUserID != "" {
			userIDs = append(userIDs, assetHandoverRequests[i].TargetAssignedUserID)
		}
	}

	if len(userIDs) > 0 {
		users, err := uc.UserRepository.GetUsersV2(ctx, uc.DB.DB(), userModels.UserCondition{
			Where: userModels.UserWhere{
				IDs: userIDs,
			},
		})
		if err != nil {
			return nil, err
		}

		for i := range users {
			mapUserName[users[i].ID] = users[i].GetName()
		}
	}

	resp := make([]dtos.AssetHandoverRequesListResp, 0, len(assetHandoverRequests))
	for _, ahr := range assetHandoverRequests {
		resp = append(resp, dtos.BuildAAssetHandoverRequestListResp(ahr, mapUserName))
	}

	return &commonmodel.ListResponse{
		TotalRecords: total,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         resp,
	}, nil
}

func (uc *AssetAssignmentUseCase) GetAssetHandoverRequestListByAssetID(ctx context.Context, assetID string, req dtos.GetAssetHandoverRequestListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	total, assetHandoverRequests, err := uc.AssetAssignmentRepository.GetAssetHandoverRequestList(ctx, uc.DB.DB(), models.GetAssetHandoverRequestListParam{
		ListRequest: req.ListRequest,
		Cond: models.AssetHandoverRequestCondition{
			Where: models.AssetHandoverRequestWhere{
				AssetID:    assetID,
				ClientID:   claim.GetLoggedInClientID(),
				StatusCode: req.StatusCode,
			},
			Preload: models.AssetHandoverRequestPreload{
				Status: true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	if len(assetHandoverRequests) == 0 {
		return &commonmodel.ListResponse{
			TotalRecords: total,
			PageSize:     req.PageSize,
			PageNo:       req.PageNo,
			Data:         nil,
		}, nil
	}

	mapUserName := map[string]string{}
	userIDs := []string{}
	for i := range assetHandoverRequests {
		if assetHandoverRequests[i].BeforeAssignedUserID != "" {
			userIDs = append(userIDs, assetHandoverRequests[i].BeforeAssignedUserID)
		}

		if assetHandoverRequests[i].TargetAssignedUserID != "" {
			userIDs = append(userIDs, assetHandoverRequests[i].TargetAssignedUserID)
		}
	}

	if len(userIDs) > 0 {
		users, err := uc.UserRepository.GetUsersV2(ctx, uc.DB.DB(), userModels.UserCondition{
			Where: userModels.UserWhere{
				IDs: userIDs,
			},
		})
		if err != nil {
			return nil, err
		}

		for i := range users {
			mapUserName[users[i].ID] = users[i].GetName()
		}
	}

	resp := make([]dtos.AssetHandoverRequestResp, 0, len(assetHandoverRequests))
	for _, ahr := range assetHandoverRequests {
		resp = append(resp, dtos.BuildAAssetHandoverRequestResp(ahr, mapUserName, nil))
	}

	return &commonmodel.ListResponse{
		TotalRecords: total,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         resp,
	}, nil
}

func (uc *AssetAssignmentUseCase) GetAssetHandoverRequest(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	assetHandoverRequest, err := uc.AssetAssignmentRepository.GetAssetHandoverRequest(ctx, uc.DB.DB(), models.AssetHandoverRequestCondition{
		Where: models.AssetHandoverRequestWhere{
			ID:         id,
			ClientID:   claim.GetLoggedInClientID(),
			StatusCode: "",
		},
	})
	if err != nil {
		return nil, err
	}

	asset, err := uc.AssetRepository.GetAsset(ctx, uc.DB.DB(), models.AssetCondition{
		Where: models.AssetWhere{
			ID: assetHandoverRequest.AssetID,
		},
		Preload: models.AssetPreload{
			Brand: true,
		},
	})
	if err != nil {
		return nil, err
	}

	mapUserName := map[string]string{}
	userIDs := []string{}
	if assetHandoverRequest.BeforeAssignedUserID != "" {
		userIDs = append(userIDs, assetHandoverRequest.BeforeAssignedUserID)
	}

	if assetHandoverRequest.TargetAssignedUserID != "" {
		userIDs = append(userIDs, assetHandoverRequest.TargetAssignedUserID)
	}

	if len(userIDs) > 0 {
		users, err := uc.UserRepository.GetUsersV2(ctx, uc.DB.DB(), userModels.UserCondition{
			Where: userModels.UserWhere{
				IDs: userIDs,
			},
		})
		if err != nil {
			return nil, err
		}

		for i := range users {
			mapUserName[users[i].ID] = users[i].GetName()
		}
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        dtos.BuildAAssetHandoverRequestResp(*assetHandoverRequest, mapUserName, asset),
	}, nil
}

func (uc *AssetAssignmentUseCase) PrintAssetHandoverRequestHTML(ctx context.Context, id string) (commonmodel.DetailResponse, error) {
	var printHTMLResponse commonmodel.DetailResponse

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return printHTMLResponse, err
	}
	clientID := claim.GetLoggedInClientID()

	// STRUCTS
	type AssetInspectionTyreTempl struct {
		AssetInspectionTyre models.AssetInspectionTyre
		AssetTyre           models.AssetTyre
		Attachments         []storageDtos.GetAttachmentsResp
		AverageRTD          float64
		TUR                 float64
		RemainingTUR        float64
		TyreBaseSvg         template.HTML
		SerialNo            string
		BrandName           string
		TyreSize            string
		Remark              string
		TreadPoint          int
		Rfid                string
		TyreWaveSvg         dtos.TyreWaveSvgResponse
	}
	type PrintLayoutData struct {
		LayoutType          string // blank space, inspection section, axle configuration
		AssetInspectionTyre AssetInspectionTyreTempl
		TyreCount           int
	}
	type PageData struct {
		AssetHandover        *models.AssetHandoverRequest
		AssetHandoverAsset   *models.Asset
		AssetHandoverVehicle *models.AssetVehicle
		Odometer             float64
		BeforeAssignedUser   *userModels.User
		TargeteAssignedUser  *userModels.User
		Form                 contentDtos.PrintHTMLForm
		NeedInspection       bool
		SignatureTime        string
		FooterNote           string
		HeaderNoTitle        string
		HeaderNo             string
		// INSPECTION RELATED
		AxleConfigurationItems            []template.HTML
		AssetInspection                   *models.AssetInspection
		AssetInspectionTyreTemplData      []AssetInspectionTyreTempl
		AssetVehicle                      models.AssetInspectionVehicle
		Asset                             models.Asset
		RemainingTyreDetailSectionNumbers []int
		PrintLayoutDataArr                [][]PrintLayoutData
		RunningTyreCount                  int
		SpareTyreCount                    int
		TyreWavePlaceholderSvg            dtos.TyreWaveSvgResponse
	}
	templateData := PageData{}

	// TYRE WAVE PLACEHOLDER
	// GENERATE PLACEHOLDER TYRE WAVE
	tyreWavePlaceholderSource := dtos.TyreWaveSource{
		NoOfInspectionPoint: 4,
		RDT1:                0,
		RDT2:                0,
		RDT3:                0,
		RDT4:                0,
		OTD:                 0,
		ShowOTD:             false,
	}
	tyreWavePlaceholderSvg := uc.AssetInspectionUtil.GenerateTyreWaveSvg(tyreWavePlaceholderSource)
	templateData.TyreWavePlaceholderSvg = tyreWavePlaceholderSvg

	// ASSET HANDOVER
	assetHandover, err := uc.AssetAssignmentRepository.GetAssetHandoverRequest(ctx, uc.DB.DB(), models.AssetHandoverRequestCondition{
		Where: models.AssetHandoverRequestWhere{
			ID:       id,
			ClientID: clientID,
		},
	})
	if err != nil {
		return printHTMLResponse, err
	}

	// ASSET VEHICLE
	assetID := assetHandover.AssetID
	assetHandoverAsset, err := uc.AssetRepository.GetAsset(ctx, uc.DB.DB(), models.AssetCondition{
		Where: models.AssetWhere{
			ID:       assetID,
			ClientID: clientID,
		},
		Preload: models.AssetPreload{
			Location:               true,
			CustomAssetCategory:    true,
			CustomAssetSubCategory: true,
		},
	})
	if err != nil {
		return printHTMLResponse, err
	}

	assetHandoverVehicle, _ := uc.AssetVehicleRepository.GetAssetVehicle(ctx, uc.DB.DB(), models.AssetVehicleCondition{
		Where: models.AssetVehicleWhere{
			AssetID:  assetID,
			ClientID: clientID,
		},
	})

	odometer := 0.0
	if assetHandoverVehicle != nil {
		odometer = assetHandoverVehicle.VehicleKM
	}

	beforeAssignedUserID := assetHandover.BeforeAssignedUserID
	beforeAssignedUser := &userModels.User{}
	if beforeAssignedUserID != "" {
		beforeAssignedUser, err = uc.UserRepository.GetUser(ctx, uc.DB.DB(), userModels.UserCondition{
			Where: userModels.UserWhere{
				ID:       assetHandover.CreatedBy,
				ClientID: clientID,
			},
			Preload: userModels.UserPreload{
				CurrentUserClientGroup: true,
			},
		})
	}

	if err != nil {
		return printHTMLResponse, err
	}

	targetAssignedUserID := assetHandover.TargetAssignedUserID
	targetAssignedUser, err := uc.UserRepository.GetUser(ctx, uc.DB.DB(), userModels.UserCondition{
		Where: userModels.UserWhere{
			ID:       targetAssignedUserID,
			ClientID: clientID,
		},
		Preload: userModels.UserPreload{
			CurrentUserClientGroup: true,
		},
	})
	if err != nil {
		return printHTMLResponse, err
	}

	// FORM
	formDtos := contentDtos.PrintHTMLForm{}
	form, err := uc.FormRepository.GetForm(ctx, uc.DB.DB(), contentModel.FormCondition{
		Where: contentModel.FormWhere{
			ReferenceID: id,
			ClientID:    clientID,
		},
		Preload: contentModel.FormPreload{
			FormFields: true,
		},
	})
	if err != nil {
		return printHTMLResponse, err
	}

	formDtos = contentDtos.PrintHTMLForm{
		ID:               form.ID,
		ReferenceID:      form.ReferenceID,
		FormCategoryCode: form.FormCategoryCode,
		FormFields:       make([]contentDtos.PrintHTMLFormField, 0, len(form.FormFields)),
		CreatedAt:        form.CreatedAt,
		StatusCode:       form.StatusCode,
	}
	for _, field := range form.FormFields {
		pgJSONBOptions := pgtypehelpers.HandleValue(field.Options)
		pgJSONBValue := pgtypehelpers.HandleValue(field.Value)
		FormFieldOptions := contentDtos.PrintHTMLFormFieldOptOrValueData{}
		FormFieldValue := contentDtos.PrintHTMLFormFieldOptOrValueData{}

		formFieldOptionJsonBytes, _ := pgJSONBOptions.MarshalJSON()
		_ = json.Unmarshal(formFieldOptionJsonBytes, &FormFieldOptions.Data)
		// TODO: Will need to refactor this
		if field.FormFieldTypeCode == contentConstants.FORM_FIELD_ATTACHMENT_CATEGORY_CODE {
			rawFormFieldOptions, ok := FormFieldOptions.Data.([]interface{})
			if !ok {
				commonlogger.Errorf("Error: JSON is not a valid slice")
				return printHTMLResponse, err
			} else {
				mapFormFieldOptions := make([]map[string]interface{}, 0, len(rawFormFieldOptions))
				for i, v := range rawFormFieldOptions {
					item, valid := v.(map[string]interface{})
					if !valid {
						commonlogger.Errorf("Error: Element %d is not a valid map", i)
						return printHTMLResponse, err
					}
					mapFormFieldOptions = append(mapFormFieldOptions, item)
				}

				for _, mapFormFieldOption := range mapFormFieldOptions {
					if mapFormFieldOption["value"].(string) != "" {
						mapFormFieldOption["value"], _ = helpers.GenerateCloudStorageSignedURL(mapFormFieldOption["value"].(string), time.Duration(24))
					}
				}

				FormFieldOptions.Data = mapFormFieldOptions
			}
		}

		formFieldValueJsonBytes, _ := pgJSONBValue.MarshalJSON()
		_ = json.Unmarshal(formFieldValueJsonBytes, &FormFieldValue.Data)

		formDtos.FormFields = append(formDtos.FormFields, contentDtos.PrintHTMLFormField{
			ID:                field.ID,
			FormFieldTypeCode: field.FormFieldTypeCode,
			Options:           FormFieldOptions,
			Sequence:          field.Sequence,
			Label:             field.Label,
			Value:             FormFieldValue,
			IsMandatory:       field.IsMandatory,
		})
	}

	if assetHandover.NeedInspection {
		// FETCH INSPECTION
		assetInspection, err := uc.AssetInspectionRepository.GetAssetInspection(
			ctx,
			uc.DB.DB(),
			models.AssetInspectionCondition{
				Where: models.AssetInspectionWhere{
					ReferenceCode: constants.ASSET_INSPECTION_REFERENCE_CODE_ASSET_HANDOVER,
					ReferenceID:   id,
					ClientID:      clientID,
				},
				Preload: models.AssetInspectionPreload{},
			},
		)
		if err != nil {
			return printHTMLResponse, err
		}

		// FETCH TYRE INSPECTION
		assetInspectionTyres := []models.AssetInspectionTyre{}
		_, err = uc.AssetInspectionTyreRepository.GetAssetInspectionTyres(
			ctx,
			uc.DB.DB(),
			&assetInspectionTyres,
			commonmodel.ListRequest{
				SearchKeyword: clientID,
				PageSize:      100,
				PageNo:        1,
			},
			assetInspection.ID,
		)
		if err != nil {
			return printHTMLResponse, err
		}

		// FETCH VEHICLE INSPECTION
		assetVehicle := models.AssetInspectionVehicle{}
		totalAssetVehicles, assetVehicles, err := uc.AssetInspectionVehicleRepository.GetAssetInspectionVehicleList(ctx, uc.DB.DB(), models.GetAssetInspectionVehicleListParam{
			Cond: models.AssetInspectionVehicleCondition{
				Where: models.AssetInspectionVehicleWhere{
					ClientID:     clientID,
					InspectionID: assetInspection.ID,
				},
				Preload: models.AssetInspectionVehiclePreload{
					AssetVehicle:             true,
					AssetVehicleAsset:        true,
					AssetVehicleVehicle:      true,
					AssetVehicleVehicleBrand: true,
				},
			},
			ListRequest: commonmodel.ListRequest{
				PageSize: 1,
				PageNo:   1,
			},
		})
		if err != nil {
			return printHTMLResponse, err
		}
		if totalAssetVehicles > 0 {
			assetVehicle = assetVehicles[0]
		}

		// FETCH AND MAP ASSET TYRE BY ID
		var assetTyreIds []string
		var assetInspectionIds []string
		for _, inspection := range assetInspectionTyres {
			assetTyreIds = append(assetTyreIds, inspection.AssetTyreID)
			assetInspectionIds = append(assetInspectionIds, inspection.ID)
		}
		assetTyres := []models.AssetTyre{}
		assetTyresMapById := map[string]models.AssetTyre{}
		err = uc.AssetTyreRepository.GetAssetTyresByIds(ctx, uc.DB.DB(), &assetTyres, assetTyreIds, models.GetAssetTyreListParam{})
		if err != nil {
			commonlogger.Errorf("Error in getting asset tyres by asset tyre ids from asset tyre service", err)
			return printHTMLResponse, err
		}
		for _, assetTyre := range assetTyres {
			assetTyresMapById[assetTyre.AssetID] = assetTyre
		}

		// FETCH ATTACHMENTS
		attachmentsMapByInspectionID := make(map[string][]storageDtos.GetAttachmentsResp, len(assetInspectionIds))
		attachments, err := uc.attachmentUsecase.GetAttachments(ctx, storageDtos.GetAttachmentsReq{
			ReferenceCode:      "ASSET_INSPECTION",
			SourceReferenceIDs: assetInspectionIds,
		})
		if err != nil {
			return printHTMLResponse, err
		}
		for _, attachment := range attachments {
			attachmentsMapByInspectionID[attachment.SourceReferenceID] = append(attachmentsMapByInspectionID[attachment.SourceReferenceID], attachment)
		}

		// REF AXLE CONFIG
		var refAxleConfiguration pgtype.JSONB
		if assetVehicle.AssetVehicle.AxleConfiguration.Status == pgtype.Present {
			refAxleConfiguration = assetVehicle.AssetVehicle.AxleConfiguration
		} else {
			refAxleConfiguration = assetVehicle.AxleConfiguration
		}

		// CONSTRUCT AXLE CONFIGURATIONS
		type axleConfiguration struct {
			value       string
			numOfTyres  int
			isSpareTyre bool
			name        string
			width       int
			height      int
		}
		axleConfigurations := map[string]axleConfiguration{
			"TWO_TYRES_WITH_STEERING_WHEEL": {
				value:       "TWO_TYRES_WITH_STEERING_WHEEL",
				numOfTyres:  2,
				isSpareTyre: false,
				name:        "axle.svg",
				width:       115,
				height:      65,
			},
			"TWO_TYRES": {
				value:       "TWO_TYRES",
				numOfTyres:  2,
				isSpareTyre: false,
				name:        "axle1.svg",
				width:       115,
				height:      65,
			},
			"TWO_TYRES_WITH_SPINDLE": {
				value:       "TWO_TYRES_WITH_SPINDLE",
				numOfTyres:  2,
				isSpareTyre: false,
				name:        "axle2.svg",
				width:       115,
				height:      65,
			},
			"FOUR_TYRES": {
				value:       "FOUR_TYRES",
				numOfTyres:  4,
				isSpareTyre: false,
				name:        "axle3.svg",
				width:       180,
				height:      65,
			},
			"FOUR_TYRES_WITH_SPINDLE": {
				value:       "FOUR_TYRES_WITH_SPINDLE",
				numOfTyres:  4,
				isSpareTyre: false,
				name:        "axle4.svg",
				width:       180,
				height:      65,
			},
			"ONE_SPARE_TYRE": {
				value:       "ONE_SPARE_TYRE",
				numOfTyres:  1,
				isSpareTyre: true,
				name:        "axle5.svg",
				width:       150,
				height:      40,
			},
		}

		remainingTyreDetailSectionCount := (len(assetInspectionTyres) + 1) % 4
		var remainingTyreDetailSectionNumbers []int
		if remainingTyreDetailSectionCount > 0 {
			remainingTyreDetailSectionCount = 4 - remainingTyreDetailSectionCount
		}
		for i := 0; i < remainingTyreDetailSectionCount; i += 1 {
			remainingTyreDetailSectionNumbers = append(remainingTyreDetailSectionNumbers, i)
		}

		// USE HTML TEMPLATING TO APPEND DATA
		var assetInspectionTyreTemplData []AssetInspectionTyreTempl
		assetInspectionTyreTemplDataMap := map[string]AssetInspectionTyreTempl{}
		deviceId := ""
		var enabledTyrePositions []int
		for _, inspection := range assetInspectionTyres {
			if deviceId == "" {
				deviceId = inspection.DeviceID
			}

			remark := "-"
			if inspection.Remark != "" {
				remark = helpers.TruncateEnd(inspection.Remark, 48)
			}

			brandName := "-"
			tyreSize := "-"
			serialNo := "-"
			rfid := "-"
			if inspection.AssetTyreID != "" {
				serialNo = helpers.TruncateMiddle(inspection.AssetTyre.Asset.SerialNumber, 11)
				if assetTyresMapById[inspection.AssetTyreID].Asset.Brand.BrandName != "" {
					brandName = assetTyresMapById[inspection.AssetTyreID].Asset.Brand.BrandName
				}
				tyreSize = assetTyresMapById[inspection.AssetTyreID].Tyre.SectionWidth + " " + assetTyresMapById[inspection.AssetTyreID].Tyre.Construction + " " + assetTyresMapById[inspection.AssetTyreID].Tyre.RimDiameter
				rfid = inspection.AssetTyre.Asset.Rfid
			} else {
				if inspection.CustomSerialNumber != "" {
					serialNo = helpers.TruncateMiddle(inspection.CustomSerialNumber, 11)
				}
				if inspection.CustomBrandName.String != "" {
					brandName = inspection.CustomBrandName.String
				}
				if inspection.CustomTyreSize != "" {
					tyreSize = inspection.CustomTyreSize
				}
				if inspection.CustomRFID != "" {
					rfid = inspection.CustomRFID
				}
			}

			withTyreOptimax := false
			withOTD := false
			treadPoint := inspection.NumberOfInspectionPoint
			if treadPoint == 0 || treadPoint > 5 {
				treadPoint = 4
			}
			RDT1 := inspection.RDT1
			RDT2 := inspection.RDT2
			RDT3 := inspection.RDT3
			RDT4 := inspection.RDT4
			averageRTD := helpers.CalculateAverageRTD(RDT1, RDT2, RDT3, RDT4)
			averageRTD = helpers.TruncateFloat(averageRTD, 1)
			var OTD float64 = 0
			if inspection.AssetTyreID != "" {
				OTD = assetTyresMapById[inspection.AssetTyreID].OriginalTd
				withOTD = true
			} else {
				OTD = RDT1
				if RDT2 > OTD {
					OTD = RDT2
				}
				if RDT3 > OTD {
					OTD = RDT3
				}
				if RDT4 > OTD {
					OTD = RDT4
				}
			}

			TUR := helpers.CalculateTyreUtilRate(OTD, averageRTD)

			tyreBaseSvg := uc.AssetInspectionUtil.GenerateTyreBaseSvg(RDT1, RDT2, RDT3, RDT4, OTD, TUR, 0.44, withTyreOptimax, true, treadPoint, withOTD)

			tyreWaveSource := dtos.TyreWaveSource{
				NoOfInspectionPoint: treadPoint,
				RDT1:                RDT1,
				RDT2:                RDT2,
				RDT3:                RDT3,
				RDT4:                RDT4,
				OTD:                 OTD,
				ShowOTD:             withOTD,
			}
			tyreWaveSvg := uc.AssetInspectionUtil.GenerateTyreWaveSvg(tyreWaveSource)

			currentTemplateData := AssetInspectionTyreTempl{
				AssetInspectionTyre: inspection,
				AssetTyre:           assetTyresMapById[inspection.AssetTyreID],
				Attachments:         attachmentsMapByInspectionID[inspection.ID],
				AverageRTD:          averageRTD,
				TUR:                 TUR,
				RemainingTUR:        100 - TUR,
				TyreBaseSvg:         tyreBaseSvg,
				SerialNo:            serialNo,
				BrandName:           brandName,
				TyreSize:            tyreSize,
				Remark:              remark,
				TreadPoint:          treadPoint,
				Rfid:                rfid,
				TyreWaveSvg:         tyreWaveSvg,
			}
			assetInspectionTyreTemplData = append(assetInspectionTyreTemplData, currentTemplateData)
			assetInspectionTyreTemplDataMap[fmt.Sprintf("%.0f", inspection.TyrePosition)] = currentTemplateData
			enabledTyrePositions = append(enabledTyrePositions, int(inspection.TyrePosition))
		}

		// GENERATE AXLE CONFIGURATIONS SVG
		var axleConfigurationItems []template.HTML
		tyreNumber := 1

		rAxleConfiguration := []models.AxleConfiguration{}
		err = refAxleConfiguration.AssignTo(&rAxleConfiguration)
		if err != nil {
			return printHTMLResponse, err
		}

		for _, assetAxleConfiguration := range rAxleConfiguration {
			axleConfigurationSvg, tyreNumberRes := uc.AssetInspectionUtil.GenerateAxleConfigurationSvg(assetAxleConfiguration.Axle, tyreNumber, 0.62, enabledTyrePositions)
			tyreNumber = tyreNumberRes

			axleConfigurationItems = append(axleConfigurationItems, axleConfigurationSvg)
		}

		// NEW LOGIC
		blankPrintLayoutData := PrintLayoutData{
			LayoutType:          "BLANK_SPACE",
			AssetInspectionTyre: AssetInspectionTyreTempl{},
			TyreCount:           0,
		}
		axleConfigPrintLayoutData := PrintLayoutData{
			LayoutType:          "AXLE_CONFIGURATION",
			AssetInspectionTyre: AssetInspectionTyreTempl{},
			TyreCount:           0,
		}
		var printLayoutDataArr [][]PrintLayoutData
		tyreCount := 1
		runningTyreCount := 0
		spareTyreCount := 0
		totalInspectedTyres := 0
		totalInspectedRunningTyres := 0
		totalInspectedSpareTyres := 0

		rAxleConfiguration = []models.AxleConfiguration{}
		err = refAxleConfiguration.AssignTo(&rAxleConfiguration)
		if err != nil {
			return printHTMLResponse, err
		}

		for idx, assetAxleConfiguration := range rAxleConfiguration {
			currentConfig := axleConfigurations[assetAxleConfiguration.Axle]
			var currentRowPrintLayoutDataArr []PrintLayoutData
			for i := 0; i < 5; i++ {
				if currentConfig.numOfTyres == 2 {
					if idx == 0 && i == 2 {
						currentRowPrintLayoutDataArr = append(currentRowPrintLayoutDataArr, axleConfigPrintLayoutData)
					} else if idx != 0 && i == 2 {
						blankPrintLayoutData.LayoutType = "AXLE_SPACER"
						currentRowPrintLayoutDataArr = append(currentRowPrintLayoutDataArr, blankPrintLayoutData)
					} else if i == 0 || i == 4 {
						blankPrintLayoutData.LayoutType = "AXLE_SIDE_SPACER"
						currentRowPrintLayoutDataArr = append(currentRowPrintLayoutDataArr, blankPrintLayoutData)
					} else {
						currentTyre := assetInspectionTyreTemplDataMap[fmt.Sprintf("%d", tyreCount)]
						inspectionNewScenario2LayoutData := PrintLayoutData{
							LayoutType:          "TYRE",
							AssetInspectionTyre: currentTyre,
							TyreCount:           tyreCount,
						}
						if currentTyre.AssetInspectionTyre.AssetInspectionID != "" {
							currentRowPrintLayoutDataArr = append(currentRowPrintLayoutDataArr, inspectionNewScenario2LayoutData)
							totalInspectedTyres++
							totalInspectedRunningTyres++
						} else {
							blankPrintLayoutData.LayoutType = "TYRE_PLACEHOLDER"
							blankPrintLayoutData.TyreCount = tyreCount
							currentRowPrintLayoutDataArr = append(currentRowPrintLayoutDataArr, blankPrintLayoutData)
						}
						tyreCount++
						runningTyreCount++
					}
				} else if currentConfig.numOfTyres == 4 {
					if i == 2 {
						blankPrintLayoutData.LayoutType = "CARD_SPACER"
						currentRowPrintLayoutDataArr = append(currentRowPrintLayoutDataArr, blankPrintLayoutData)
					} else {
						currentTyre := assetInspectionTyreTemplDataMap[fmt.Sprintf("%d", tyreCount)]
						inspectionNewScenario2LayoutData := PrintLayoutData{
							LayoutType:          "TYRE",
							AssetInspectionTyre: currentTyre,
							TyreCount:           tyreCount,
						}
						if currentTyre.AssetInspectionTyre.AssetInspectionID != "" {
							currentRowPrintLayoutDataArr = append(currentRowPrintLayoutDataArr, inspectionNewScenario2LayoutData)
							totalInspectedTyres++
							totalInspectedRunningTyres++
						} else {
							blankPrintLayoutData.LayoutType = "TYRE_PLACEHOLDER"
							blankPrintLayoutData.TyreCount = tyreCount
							currentRowPrintLayoutDataArr = append(currentRowPrintLayoutDataArr, blankPrintLayoutData)
						}
						tyreCount++
						runningTyreCount++
					}
				} else {
					if i == 2 {
						currentTyre := assetInspectionTyreTemplDataMap[fmt.Sprintf("%d", tyreCount)]
						inspectionNewScenario2LayoutData := PrintLayoutData{
							LayoutType:          "SPARE_TYRE",
							AssetInspectionTyre: currentTyre,
							TyreCount:           tyreCount,
						}
						if currentTyre.AssetInspectionTyre.AssetInspectionID != "" {
							currentRowPrintLayoutDataArr = append(currentRowPrintLayoutDataArr, inspectionNewScenario2LayoutData)
							totalInspectedTyres++
							totalInspectedSpareTyres++
						} else {
							blankPrintLayoutData.LayoutType = "SPARE_TYRE_PLACEHOLDER"
							blankPrintLayoutData.TyreCount = tyreCount
							currentRowPrintLayoutDataArr = append(currentRowPrintLayoutDataArr, blankPrintLayoutData)
						}
						tyreCount++
						spareTyreCount++
					}
				}
			}
			printLayoutDataArr = append(printLayoutDataArr, currentRowPrintLayoutDataArr)
		}

		templateData.AxleConfigurationItems = axleConfigurationItems
		templateData.AssetInspection = assetInspection
		templateData.AssetInspectionTyreTemplData = assetInspectionTyreTemplData
		templateData.AssetVehicle = assetVehicle
		templateData.Asset = assetVehicle.AssetVehicle.Asset
		templateData.RemainingTyreDetailSectionNumbers = remainingTyreDetailSectionNumbers
		templateData.PrintLayoutDataArr = printLayoutDataArr
		templateData.RunningTyreCount = runningTyreCount
		templateData.SpareTyreCount = spareTyreCount
	}

	// ADDITIONAL DATA
	signatureTime := time.Now().Format("02 Jan 2006")
	targetFullName := targetAssignedUser.FirstName
	if targetAssignedUser.LastName != "" {
		targetFullName += " " + targetAssignedUser.LastName
	}
	footerTargetName := "Ditugaskan ke " + targetFullName
	footerFinishedDate := " • Waktu Diselesaikan " + assetHandover.UpdatedAt.Format("2 Jan 2006 15:04:05")
	footerNote := footerTargetName + footerFinishedDate

	// OVERALL TEMPLATE DATA
	templateData.AssetHandover = assetHandover
	templateData.AssetHandoverAsset = assetHandoverAsset
	templateData.AssetHandoverVehicle = assetHandoverVehicle
	templateData.Odometer = odometer
	templateData.BeforeAssignedUser = beforeAssignedUser
	templateData.TargeteAssignedUser = targetAssignedUser
	templateData.Form = formDtos
	templateData.NeedInspection = assetHandover.NeedInspection
	templateData.SignatureTime = signatureTime
	templateData.FooterNote = footerNote

	// HEADER NO
	headerNoTitle := "Nomor Pelat/Seri"
	headerNo := "-"
	if assetHandoverAsset.ReferenceNumber != "" && assetHandoverAsset.SerialNumber != "" {
		headerNo = fmt.Sprintf("%s/%s", assetHandoverAsset.ReferenceNumber, assetHandoverAsset.SerialNumber)
	} else if assetHandoverAsset.ReferenceNumber != "" {
		headerNoTitle = "Nomor Pelat"
		headerNo = assetHandoverAsset.ReferenceNumber
	} else if assetHandoverAsset.SerialNumber != "" {
		headerNoTitle = "Nomor Seri"
		headerNo = assetHandoverAsset.SerialNumber
	}
	templateData.HeaderNoTitle = headerNoTitle
	templateData.HeaderNo = headerNo

	templateHTML := template.Must(template.ParseFiles("./statics/print-template/asset_handover.html"))
	var templateBuff bytes.Buffer
	err = templateHTML.Execute(&templateBuff, &templateData)
	if err != nil {
		return printHTMLResponse, err
	}

	templateHTMLString := templateBuff.String()

	// EXPORT AS HTML FILE
	file, err := os.Create("asset_handover_req.html")
	if err != nil {
		commonlogger.Errorf("Failed to create file:", err)
		return printHTMLResponse, err
	}
	defer file.Close()

	_, err = file.WriteString(templateBuff.String())
	if err != nil {
		commonlogger.Errorf("Failed to write to file:", err)
		return printHTMLResponse, err
	}

	printHTMLResponse = commonmodel.DetailResponse{
		Success:     true,
		Message:     "Successfully printed asset handover request HTML",
		ReferenceID: id,
		Data:        templateHTMLString,
	}

	return printHTMLResponse, nil
}
