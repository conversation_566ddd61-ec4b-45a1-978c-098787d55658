package usecase

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/errorconstants"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	storageConstants "assetfindr/internal/app/storage/constants"
	storageUsecase "assetfindr/internal/app/storage/usecase"
	userIdentityRepository "assetfindr/internal/app/user-identity/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"context"
	"strings"

	"assetfindr/internal/infrastructure/database"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
)

type DigispectUseCase struct {
	DB                               database.DBUsecase
	DigispectRepo                    repository.DigispectRepository
	UserRepository                   userIdentityRepository.UserRepository
	AssetInspectionTyreRepository    repository.AssetInspectionTyreRepository
	AssetInspectionVehicleRepository repository.AssetInspectionVehicleRepository
	storageUsecase                   *storageUsecase.AttachmentUseCase
}

func NewDigispectUseCase(
	DB database.DBUsecase,
	digispectRepo repository.DigispectRepository,
	assetInspectionTyreRepository repository.AssetInspectionTyreRepository,
	assetInspectionVehicleRepository repository.AssetInspectionVehicleRepository,
	storageUsecase *storageUsecase.AttachmentUseCase,
	userRepository userIdentityRepository.UserRepository,
) *DigispectUseCase {
	return &DigispectUseCase{
		DB:                               DB,
		DigispectRepo:                    digispectRepo,
		AssetInspectionTyreRepository:    assetInspectionTyreRepository,
		AssetInspectionVehicleRepository: assetInspectionVehicleRepository,
		storageUsecase:                   storageUsecase,
		UserRepository:                   userRepository,
	}
}

func (uc *DigispectUseCase) GetDigispectConfigList(ctx context.Context, req dtos.DigispectConfigListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	total, digiConfigs, err := uc.DigispectRepo.GetDigispectConfigList(ctx, uc.DB.DB(), models.DigispectConfigParam{
		ListRequest: req.ListRequest,
		Cond: models.DigispectConfigCond{
			Where: models.DigispectConfigWhere{
				ClientID:  claim.GetLoggedInClientID(),
				TypeCodes: req.TypeCodes,
			},
			Preload: models.DigispectConfigPreload{
				DigispectType:    true,
				BrandDigiscpect:  true,
				PatternDigispect: true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	data := []dtos.DigispectConfigListResp{}

	for _, digispect := range digiConfigs {
		patternID := ""
		if digispect.PatternDigispectID.Valid {
			patternID = digispect.PatternDigispectID.String
		}

		data = append(data, dtos.DigispectConfigListResp{
			ID:                 digispect.ID,
			TypeCode:           digispect.TypeCode,
			DigispectType:      digispect.DigispectType,
			ModelConfig:        digispect.ModelConfig.String,
			SizeConfig:         digispect.SizeConfig.String,
			StatusCode:         digispect.StatusCode,
			BrandDigispectID:   digispect.BrandDigispectID,
			BrandDigiscpect:    digispect.BrandDigiscpect,
			PatternDigispectID: patternID,
			PatternDigispect:   digispect.PatternDigispect,
		})
	}

	resp := &commonmodel.ListResponse{
		TotalRecords: total,
		PageSize:     len(digiConfigs),
		PageNo:       req.PageNo,
		Data:         data,
	}

	return resp, nil
}

func (uc *DigispectUseCase) CreateDigispectConfig(ctx context.Context, req dtos.CreateDigispectConfigReq) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	digispectConf := &models.DigispectConfig{
		BrandDigispectID: req.BrandID,
		TypeCode:         req.TypeCode,
		ModelConfig:      null.StringFrom(req.ModelName),
		SizeConfig:       null.StringFrom(req.Size),
		ModelV2: commonmodel.ModelV2{
			ClientID: claim.GetLoggedInClientID(),
		},
		StatusCode: constants.DIGISPECT_STATUS_ACTIVE,
	}
	if req.PatternID != "" {
		digispectConf.PatternDigispectID = null.StringFrom(req.PatternID)
	}

	// brand
	brand, err := uc.DigispectRepo.GetDigispectBrand(ctx, uc.DB.DB(), models.DigispectBrandConfigParam{
		Cond: models.DigispectBrandConfigCond{
			Where: models.DigispectBrandConfigWhere{
				ClientID:   claim.GetLoggedInClientID(),
				ID:         req.BrandID,
				TypeCodes:  []string{req.TypeCode},
				StatusCode: constants.DIGISPECT_STATUS_ACTIVE,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	// pattern
	pattern, err := uc.DigispectRepo.GetDigispectPattern(ctx, uc.DB.DB(), models.DigispectPatternConfigParam{
		Cond: models.DigispectPatternConfigCond{
			Where: models.DigispectPatternConfigWhere{
				ClientID:   claim.GetLoggedInClientID(),
				ID:         req.PatternID,
				StatusCode: constants.DIGISPECT_STATUS_ACTIVE,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	err = uc.DigispectRepo.UpsertDigispectConfig(ctx, uc.DB.DB(), digispectConf)
	if err != nil {
		return nil, err
	}

	digispectConf.BrandDigiscpect = *brand
	digispectConf.PatternDigispect = *pattern

	resp := &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success Create Digispect Config",
		ReferenceID: digispectConf.ID,
		Data:        digispectConf,
	}

	return resp, nil
}

func (uc *DigispectUseCase) UpdateDigispectConfig(ctx context.Context, req dtos.CreateDigispectConfigReq) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	configOld, err := uc.DigispectRepo.GetDigispectConfig(ctx, uc.DB.DB(), models.DigispectConfigParam{
		Cond: models.DigispectConfigCond{
			Where: models.DigispectConfigWhere{
				ClientID: claim.GetLoggedInClientID(),
				ID:       req.DigispectConfigID,
			},
			Preload: models.DigispectConfigPreload{
				DigispectType:   true,
				BrandDigiscpect: true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	brandIDWhere := configOld.BrandDigispectID
	if configOld.BrandDigispectID != req.BrandID {
		brandIDWhere = req.BrandID
	}

	digiBrand, err := uc.DigispectRepo.GetDigispectBrand(ctx, uc.DB.DB(), models.DigispectBrandConfigParam{
		Cond: models.DigispectBrandConfigCond{
			Where: models.DigispectBrandConfigWhere{
				ID:       brandIDWhere,
				ClientID: claim.GetLoggedInClientID(),
			},
		},
	})
	if err != nil {
		return nil, err
	}

	// pattern
	_, err = uc.DigispectRepo.GetDigispectPattern(ctx, uc.DB.DB(), models.DigispectPatternConfigParam{
		Cond: models.DigispectPatternConfigCond{
			Where: models.DigispectPatternConfigWhere{
				ID:       req.PatternID,
				ClientID: claim.GetLoggedInClientID(),
			},
		},
	})
	if err != nil {
		return nil, err
	}

	brands := []string{}
	isNewBrand := false
	if len(digiBrand.TypeCodes) == 1 {
		if digiBrand.TypeCodes[0] != req.TypeCode {
			brands = append(brands, digiBrand.TypeCodes[0])
			isNewBrand = true
		}
	}

	if isNewBrand {
		brands = append(brands, req.TypeCode)
		err := uc.DigispectRepo.UpsertDigispectBrand(ctx, uc.DB.DB(), &models.DigispectBrandConfig{
			ModelV2:    commonmodel.ModelV2{ID: req.BrandID},
			BrandName:  digiBrand.BrandName,
			StatusCode: digiBrand.StatusCode,
			TypeCodes:  brands,
		})
		if err != nil {
			return nil, err
		}
	}

	digispectConf := &models.DigispectConfig{
		ModelConfig:      null.StringFrom(req.ModelName),
		SizeConfig:       null.StringFrom(req.Size),
		StatusCode:       req.StatusCode,
		TypeCode:         req.TypeCode,
		BrandDigispectID: req.BrandID,
	}
	if req.PatternID != "" {
		digispectConf.PatternDigispectID = null.StringFrom(req.PatternID)
	}
	if configOld.TypeCode != req.TypeCode {
		if req.TypeCode == constants.DIGISPECT_TYPE_VEHICLE {
			digispectConf.SizeConfig = null.StringFrom("")
		} else {
			digispectConf.ModelConfig = null.StringFrom("")
		}
	}

	err = uc.DigispectRepo.UpdateDigispectConfig(ctx, uc.DB.DB(), digispectConf, models.DigispectConfigParam{
		Cond: models.DigispectConfigCond{
			Where: models.DigispectConfigWhere{
				ID:       req.DigispectConfigID,
				ClientID: claim.GetLoggedInClientID(),
			},
		},
	})
	if err != nil {
		return nil, err
	}

	resp := &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success Update Brand Digistpect",
		ReferenceID: digispectConf.ID,
		Data:        digispectConf,
	}

	return resp, nil
}

func (uc *DigispectUseCase) DelteDigispectConfig(ctx context.Context, id string) (*commonmodel.DeleteResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	config, err := uc.DigispectRepo.GetDigispectConfig(ctx, uc.DB.DB(), models.DigispectConfigParam{
		Cond: models.DigispectConfigCond{
			Where: models.DigispectConfigWhere{
				ClientID: claim.GetLoggedInClientID(),
				ID:       id,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	// CHECK IF IN USE
	totalTyreInspections, _, err := uc.AssetInspectionTyreRepository.GetAssetInspectionTyreList(
		ctx,
		uc.DB.DB(),
		models.GetAssetInspectionTyreListParam{
			ListRequest: commonmodel.ListRequest{
				PageNo:   1,
				PageSize: 1,
			},
			Cond: models.AssetInspectionTyreCondition{
				Where: models.AssetInspectionTyreWhere{
					DigispectConfID: config.ID,
				},
			},
		},
	)
	if err != nil {
		return nil, err
	}
	if totalTyreInspections > 0 {
		err = errorhandler.ErrBadRequest("DIGISPECT_CONFIG_IS_IN_USE")
		return nil, err
	}

	totalVehicleInspections, _, err := uc.AssetInspectionVehicleRepository.GetAssetInspectionVehicleList(
		ctx,
		uc.DB.DB(),
		models.GetAssetInspectionVehicleListParam{
			ListRequest: commonmodel.ListRequest{
				PageNo:   1,
				PageSize: 1,
			},
			Cond: models.AssetInspectionVehicleCondition{
				Where: models.AssetInspectionVehicleWhere{
					DigispectConfIDs: []string{config.ID},
				},
			},
		},
	)
	if err != nil {
		return nil, err
	}
	if totalVehicleInspections > 0 {
		err = errorhandler.ErrBadRequest("DIGISPECT_CONFIG_IS_IN_USE")
		return nil, err
	}

	// DELETE
	err = uc.DigispectRepo.DeleteDigispectConfig(ctx, uc.DB.DB(), []string{id})
	if err != nil {
		return nil, err
	}

	resp := &commonmodel.DeleteResponse{
		Success:     true,
		Message:     "Success Delete Digispect Config",
		ReferenceID: config.ID,
	}

	return resp, nil
}

// Brand
func (uc *DigispectUseCase) GetDigispectBrandsConfigList(ctx context.Context, req dtos.DigispectBrandConfigListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	total, digiBrands, err := uc.DigispectRepo.GetDigispectBrandList(ctx, uc.DB.DB(), models.DigispectBrandConfigParam{
		ListRequest: req.ListRequest,
		Cond: models.DigispectBrandConfigCond{
			Where: models.DigispectBrandConfigWhere{
				ClientID:  claim.GetLoggedInClientID(),
				LowerName: strings.ToLower(req.LowerName),
				TypeCodes: req.TypeCodes,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	data := []dtos.DigispectBrandConfigListResp{}

	for _, digiBrand := range digiBrands {
		data = append(data, dtos.DigispectBrandConfigListResp{
			ID:         digiBrand.ID,
			BrandName:  digiBrand.BrandName,
			TypeCode:   digiBrand.TypeCodes,
			StatusCode: digiBrand.StatusCode,
		})
	}

	resp := &commonmodel.ListResponse{
		TotalRecords: total,
		PageSize:     len(digiBrands),
		PageNo:       req.PageNo,
		Data:         data,
	}

	return resp, nil
}

func (uc *DigispectUseCase) CreateDigispectBrandsConfig(ctx context.Context, req dtos.CreateDigispectBrandReq) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	digiBrand := &models.DigispectBrandConfig{
		BrandName:  req.BrandName,
		TypeCodes:  req.TypeCode,
		StatusCode: req.StatusCode,
		ModelV2: commonmodel.ModelV2{
			ClientID: claim.GetLoggedInClientID(),
		},
	}

	err = uc.DigispectRepo.UpsertDigispectBrand(ctx, uc.DB.DB(), digiBrand)
	if err != nil {
		return nil, err
	}

	resp := &commonmodel.CreateResponse{
		Success: true,
		Message: "Success Create Brand Digistpect",
		Data:    digiBrand,
	}

	return resp, nil
}

func (uc *DigispectUseCase) UpdateDigispectBrandsConfig(ctx context.Context, req dtos.UpdateDigispectBrandReq) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	digiBrand := &models.DigispectBrandConfig{
		StatusCode: req.StatusCode,
		ModelV2:    commonmodel.ModelV2{ID: req.BrandID, ClientID: claim.GetLoggedInClientID()},
		BrandName:  req.BrandName,
		TypeCodes:  req.TypeCode,
	}

	err = uc.DigispectRepo.UpsertDigispectBrand(ctx, uc.DB.DB(), digiBrand)
	if err != nil {
		return nil, err
	}

	reps := dtos.CreateDigispectBrandResp{
		BrandID:    digiBrand.ID,
		BrandName:  digiBrand.BrandName,
		TypeCode:   digiBrand.TypeCodes,
		StatusCode: digiBrand.StatusCode,
	}

	resp := &commonmodel.CreateResponse{
		Success: true,
		Message: "Success Update Brand Digistpect",
		Data:    reps,
	}

	return resp, nil
}

// Pattern
func (uc *DigispectUseCase) GetDigispectPatternConfigList(ctx context.Context, req dtos.DigispectPatternConfigListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	total, patterns, err := uc.DigispectRepo.GetDigispectPatternList(ctx, uc.DB.DB(), models.DigispectPatternConfigParam{
		ListRequest: req.ListRequest,
		Cond: models.DigispectPatternConfigCond{
			Where: models.DigispectPatternConfigWhere{
				ClientID:    claim.GetLoggedInClientID(),
				StatusCode:  req.StatusCode,
				StatusCodes: req.StatusCodes,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	data := []dtos.DigispectPatternConfigListResp{}

	for _, pattern := range patterns {
		data = append(data, dtos.DigispectPatternConfigListResp{
			ID:          pattern.ID,
			PatternName: pattern.PatternName,
			StatusCode:  pattern.StatusCode,
		})
	}

	resp := &commonmodel.ListResponse{
		TotalRecords: total,
		PageSize:     len(patterns),
		PageNo:       req.PageNo,
		Data:         data,
	}

	return resp, nil
}

func (uc *DigispectUseCase) CreateDigispectPatternConfig(ctx context.Context, req dtos.CreateDigispectPatternConfigReq) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	pattern := &models.DigispectPatternConfig{
		PatternName: req.PatternName,
		StatusCode:  req.StatusCode,
		ModelV2: commonmodel.ModelV2{
			ClientID: claim.GetLoggedInClientID(),
		},
	}

	err = uc.DigispectRepo.UpsertDigispectPatternConfig(ctx, uc.DB.DB(), pattern)
	if err != nil {
		return nil, err
	}

	resp := &commonmodel.CreateResponse{
		Success: true,
		Message: "Success Create Pattern Digistpect",
		Data:    pattern,
	}

	return resp, nil
}

func (uc *DigispectUseCase) UpdateDigispectPatternConfig(ctx context.Context, req dtos.UpdateDigispectPatternConfigReq) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	pattern := &models.DigispectPatternConfig{
		StatusCode:  req.StatusCode,
		ModelV2:     commonmodel.ModelV2{ID: req.PatternID, ClientID: claim.GetLoggedInClientID()},
		PatternName: req.PatternName,
	}

	err = uc.DigispectRepo.UpsertDigispectPatternConfig(ctx, uc.DB.DB(), pattern)
	if err != nil {
		return nil, err
	}

	reps := dtos.CreateDigispectPatternConfigResp{
		PatternID:   pattern.ID,
		PatternName: pattern.PatternName,
		StatusCode:  pattern.StatusCode,
	}

	resp := &commonmodel.CreateResponse{
		Success: true,
		Message: "Success Update Pattern Digistpect",
		Data:    reps,
	}

	return resp, nil
}

func (uc *DigispectUseCase) CreateDigispectVehicle(ctx context.Context, req dtos.CreateDigispectVehicleReq) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	err = uc.validateDigispectVehicleReferenceNumber(ctx, req.ReferenceNumber)
	if err != nil {
		return nil, err
	}

	digispectVehicle := &models.DigispectVehicle{
		ReferenceNumber:      req.ReferenceNumber,
		PartnerOwnerName:     req.PartnerOwnerName,
		DigispectBrandID:     &req.DigispectBrandID,
		DigispectBrandName:   req.DigispectBrandName,
		DigispectConfigID:    &req.DigispectConfigID,
		DigispectConfigModel: req.DigispectConfigModel,
		AxleConfiguration:    req.AxleConfiguration,
	}

	if digispectVehicle.AxleConfiguration.Status == pgtype.Undefined {
		digispectVehicle.AxleConfiguration.Status = pgtype.Null
	}

	if req.Photo != "" {
		destPhoto, err := uc.storageUsecase.MovePhotoStorage(ctx, claim.GetLoggedInClientID(),
			storageConstants.ATTACHMENT_REFERENCE_CODE_DIGISPECT_VEHICLE, req.Photo)
		if err != nil {
			return nil, err
		}

		digispectVehicle.Photo = null.StringFrom(destPhoto)
	}

	err = uc.DigispectRepo.CreateDigispectVehicle(ctx, uc.DB.WithCtx(ctx).DB(), digispectVehicle)
	if err != nil {
		return nil, err
	}

	resp := &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: digispectVehicle.ID,
		Data:        nil,
	}

	return resp, nil
}
func (uc *DigispectUseCase) validateDigispectVehicleReferenceNumber(ctx context.Context, referenceNumber string) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}

	_, err = uc.DigispectRepo.GetDigispectVehicle(ctx, uc.DB.DB(), models.DigispectVehicleCondition{
		Where: models.DigispectVehicleWhere{
			LowerReferenceNumber: referenceNumber,
			ClientID:             claim.GetLoggedInClientID(),
		},
	})
	if err == nil {
		return errorhandler.ErrBadRequest(errorconstants.ErrReferenceNumberAlreadyExist)
	}
	return nil
}

func (uc *DigispectUseCase) UpdateDigispectVehicle(ctx context.Context, id string, req dtos.UpdateDigispectVehicleReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	currentVehicle, err := uc.DigispectRepo.GetDigispectVehicle(ctx, uc.DB.DB(), models.DigispectVehicleCondition{
		Where: models.DigispectVehicleWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	if !strings.EqualFold(req.ReferenceNumber, currentVehicle.ReferenceNumber) {
		err := uc.validateDigispectVehicleReferenceNumber(ctx, req.ReferenceNumber)
		if err != nil {
			return nil, err
		}
	}

	digispectVehicle := &models.DigispectVehicle{
		ReferenceNumber:      req.ReferenceNumber,
		PartnerOwnerName:     req.PartnerOwnerName,
		DigispectBrandID:     &req.DigispectBrandID,
		DigispectBrandName:   req.DigispectBrandName,
		DigispectConfigID:    &req.DigispectConfigID,
		DigispectConfigModel: req.DigispectConfigModel,
		AxleConfiguration:    req.AxleConfiguration,
	}

	if commonmodel.IsInTempFileLoc(req.Photo) {
		destPhoto, err := uc.storageUsecase.MovePhotoStorage(ctx, claim.GetLoggedInClientID(),
			storageConstants.ATTACHMENT_REFERENCE_CODE_DIGISPECT_VEHICLE, req.Photo)
		if err != nil {
			return nil, err
		}
		digispectVehicle.Photo = null.StringFrom(destPhoto)
	} else if req.Photo == "" {
		digispectVehicle.Photo = null.StringFrom("")
	}

	err = uc.DigispectRepo.UpdateDigispectVehicle(ctx, uc.DB.WithCtx(ctx).DB(), id, digispectVehicle)
	if err != nil {
		return nil, err
	}

	resp := &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}

	return resp, nil
}

func (uc *DigispectUseCase) UpdateDigispectVehicleAxleConfiguration(ctx context.Context, id string, req dtos.UpdateDigispectVehicleAxleConfigurationReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	_, err = uc.DigispectRepo.GetDigispectVehicle(ctx, uc.DB.DB(), models.DigispectVehicleCondition{
		Where: models.DigispectVehicleWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	digispectVehicle := &models.DigispectVehicle{
		AxleConfiguration: req.AxleConfiguration,
	}

	err = uc.DigispectRepo.UpdateDigispectVehicle(ctx, uc.DB.WithCtx(ctx).DB(), id, digispectVehicle)
	if err != nil {
		return nil, err
	}

	resp := &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}

	return resp, nil
}

func (uc *DigispectUseCase) DeleteDigispectVehicle(ctx context.Context, id string) (*commonmodel.DeleteResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	_, err = uc.DigispectRepo.GetDigispectVehicle(ctx, uc.DB.DB(), models.DigispectVehicleCondition{
		Where: models.DigispectVehicleWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	err = uc.DigispectRepo.DeleteDigispectVehicle(ctx, uc.DB.WithCtx(ctx).DB(), id)
	if err != nil {
		return nil, err
	}

	resp := &commonmodel.DeleteResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
	}

	return resp, nil
}

func (uc *DigispectUseCase) GetDigispectVehicleList(ctx context.Context, req dtos.DigispectVehicleListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	total, digispectVehicles, err := uc.DigispectRepo.GetDigispectVehicleList(ctx, uc.DB.DB(), models.GetDigispectVehicleListParam{
		ListRequest: req.ListRequest,
		Cond: models.DigispectVehicleCondition{
			Where: models.DigispectVehicleWhere{
				ClientID: claim.GetLoggedInClientID(),
			},
		},
	})
	if err != nil {
		return nil, err
	}

	for i := range digispectVehicles {
		if digispectVehicles[i].Photo.String != "" {
			signedURL, err := uc.storageUsecase.GetFileSignedURL(ctx, digispectVehicles[i].Photo.String)
			if err == nil {
				digispectVehicles[i].Photo = null.StringFrom(signedURL)
			} else {
				commonlogger.Warnf("err get attachment path: %s err: %v",
					digispectVehicles[i].Photo.String, err)
			}

		}
	}

	resp := &commonmodel.ListResponse{
		TotalRecords: total,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         dtos.BuildDigispectVehicleListResponse(digispectVehicles),
	}

	return resp, nil
}

func (uc *DigispectUseCase) GetDigispectVehicleCustomers(ctx context.Context, req dtos.DigispectVehicleCustomerListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	total, customers, err := uc.DigispectRepo.GetDigispectVehicleCustomers(ctx, uc.DB.DB(), models.GetDigispectVehicleCustomerListParam{
		ListRequest: req.ListRequest,
		ClientID:    claim.GetLoggedInClientID(),
	})
	if err != nil {
		return nil, err
	}

	resp := &commonmodel.ListResponse{
		TotalRecords: total,
		PageSize:     len(customers),
		PageNo:       req.PageNo,
		Data:         customers,
	}

	return resp, nil
}

func (uc *DigispectUseCase) GetDigispectPackages(ctx context.Context) (*commonmodel.ListResponse, error) {
	digispectPackages, err := uc.UserRepository.GetDigispectPackages(ctx, uc.DB.DB())
	if err != nil {
		return nil, err
	}

	return &commonmodel.ListResponse{
		TotalRecords: len(digispectPackages),
		PageSize:     len(digispectPackages),
		PageNo:       1,
		Data:         digispectPackages,
	}, nil
}

func (uc *DigispectUseCase) ChartTop5DigispectVehicleBrands(ctx context.Context, req dtos.InspectionChartReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.DigispectRepo.ChartTop5DigispectVehicleBrands(ctx, uc.DB.DB(), claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}
