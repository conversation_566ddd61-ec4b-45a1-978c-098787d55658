package utils

import (
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/pkg/common/helpers"
	"bytes"
	"fmt"
	"html/template"
	"math"

	svg "github.com/ajstarks/svgo"
)

func generateTyreWaveBase2TemplateHTML(treadColor string, tyreWaveWidth float64) template.HTML {
	var svgBuffer bytes.Buffer

	canvasHeight := 50.0 / 160.0 * tyreWaveWidth
	canvasViewBox := fmt.Sprintf(`viewBox="0 0 160 50"`)

	// CANVAS START
	canvas := svg.New(&svgBuffer)
	canvas.Start(int(tyreWaveWidth), int(canvasHeight), `fill="none"`, canvasViewBox)
	// CANVAS CONTENT
	canvas.Path("M160 0H0V50C0 36.1929 35.8172 25 80 25C124.183 25 160 36.1929 160 50V0Z", `fill-rule="evenodd"`, fmt.Sprintf(`fill="%s"`, treadColor), `clip-rule="evenodd"`)
	// CANVAS END
	canvas.End()

	svgString := svgBuffer.String()
	svgHtml := template.HTML(svgString)

	return svgHtml
}

func generateTyreWaveBase3TemplateHTML(treadColor string, tyreWaveWidth float64) template.HTML {
	var svgBuffer bytes.Buffer

	canvasHeight := 50.0 / 212.0 * tyreWaveWidth
	canvasViewBox := fmt.Sprintf(`viewBox="0 0 212 50"`)

	// CANVAS START
	canvas := svg.New(&svgBuffer)
	canvas.Start(int(tyreWaveWidth), int(canvasHeight), `fill="none"`, canvasViewBox)
	// CANVAS CONTENT
	canvas.Path("M212 0H0V50C0 36.1929 47.4578 25 106 25C164.542 25 212 36.1929 212 50V0Z", `fill-rule="evenodd"`, fmt.Sprintf(`fill="%s"`, treadColor), `clip-rule="evenodd"`)
	// CANVAS END
	canvas.End()

	svgString := svgBuffer.String()
	svgHtml := template.HTML(svgString)

	return svgHtml
}

func generateTyreWaveBase4TemplateHTML(treadColor string, tyreWaveWidth float64) template.HTML {
	var svgBuffer bytes.Buffer

	canvasHeight := 50.0 / 264.0 * tyreWaveWidth
	canvasViewBox := fmt.Sprintf(`viewBox="0 0 264 50"`)

	// CANVAS START
	canvas := svg.New(&svgBuffer)
	canvas.Start(int(tyreWaveWidth), int(canvasHeight), `fill="none"`, canvasViewBox)
	// CANVAS CONTENT
	canvas.Path("M264 0H0V50C0 36.1929 59.0984 25 132 25C204.902 25 264 36.1929 264 50V0Z", `fill-rule="evenodd"`, fmt.Sprintf(`fill="%s"`, treadColor), `clip-rule="evenodd"`)
	// CANVAS END
	canvas.End()

	svgString := svgBuffer.String()
	svgHtml := template.HTML(svgString)

	return svgHtml
}

func generateTyreWaveFillStartTemplateHTML(treadColor string, width float64, heightPercentage float64) template.HTML {
	var svgBuffer bytes.Buffer

	// PRE CALCULATION
	heightPercentage = math.Min(100, math.Max(0, heightPercentage))
	viewBoxHeight := 36.0
	fillHeight := (heightPercentage / 100.0) * viewBoxHeight
	fillY := viewBoxHeight - fillHeight

	radius := 0.0
	if heightPercentage < 100 {
		radius = 20.0
	}

	canvasHeight := 36.0 / 48.0 * width
	canvasViewBox := fmt.Sprintf(`viewBox="0 0 48 36"`)

	// CANVAS START
	canvas := svg.New(&svgBuffer)
	canvas.Start(int(width), int(canvasHeight), `fill="none"`, canvasViewBox, `id="fill-start"`)
	// CANVAS CONTENT
	pathD := fmt.Sprintf(`M 0 %v A %v %v 0 0 1 %v %v L 48 %v L 48 36 L 0 36 Z`, (fillY + radius), radius, radius, radius, fillY, fillY)
	canvas.Path(pathD, fmt.Sprintf(`fill="%s"`, treadColor), `clip-path="url(#clipPath)"`)
	canvas.Def()
	canvas.ClipPath(`id="clipPath"`)
	canvas.Path("M3.01384 18.3637C4.09058 8.90378 12.0027 1.70144 21.5219 1.516L43.9416 1.07925C45.621 1.04653 47 2.39896 47 4.07868V35H1.12028L3.01384 18.3637Z")
	canvas.ClipEnd()
	canvas.DefEnd()
	canvas.Path("M3.01384 18.3637C4.09058 8.90378 12.0027 1.70144 21.5219 1.516L43.9416 1.07925C45.621 1.04653 47 2.39896 47 4.07868V35H1.12028L3.01384 18.3637Z", fmt.Sprintf(`stroke="%s"`, treadColor), `stroke-width="2"`, `fill="none"`)
	// CANVAS END
	canvas.End()

	svgString := svgBuffer.String()
	svgHtml := template.HTML(svgString)

	return svgHtml
}

func generateTyreWaveFillEndTemplateHTML(treadColor string, width float64, heightPercentage float64) template.HTML {
	var svgBuffer bytes.Buffer

	// PRE CALCULATION
	heightPercentage = math.Min(100, math.Max(0, heightPercentage))
	viewBoxHeight := 36.0
	fillHeight := (heightPercentage / 100.0) * viewBoxHeight
	fillY := viewBoxHeight - fillHeight

	radius := 0.0
	if heightPercentage < 100 {
		radius = 20.0
	}

	canvasHeight := 36.0 / 48.0 * width
	canvasViewBox := fmt.Sprintf(`viewBox="0 0 48 36"`)

	// CANVAS START
	canvas := svg.New(&svgBuffer)
	canvas.Start(int(width), int(canvasHeight), `fill="none"`, canvasViewBox, `id="fill-start"`)
	// CANVAS CONTENT
	pathD := fmt.Sprintf(`M 0 %v L %v %v A %v %v 0 0 1 48 %v L 48 %v L 48 36 L 0 36 Z`, fillY, (48.0 - radius), fillY, radius, radius, (fillY + radius), (fillY + radius))
	canvas.Path(pathD, fmt.Sprintf(`fill="%s"`, treadColor), `clip-path="url(#clipPath4)"`)
	canvas.Def()
	canvas.ClipPath(`id="clipPath4"`)
	canvas.Path("M44.9862 18.3637C43.9094 8.90378 35.9973 1.70144 26.4781 1.516L4.05844 1.07925C2.37903 1.04653 1 2.39896 1 4.07868V35H46.8797L44.9862 18.3637Z")
	canvas.ClipEnd()
	canvas.DefEnd()
	canvas.Path("M44.9862 18.3637C43.9094 8.90378 35.9973 1.70144 26.4781 1.516L4.05844 1.07925C2.37903 1.04653 1 2.39896 1 4.07868V35H46.8797L44.9862 18.3637Z", fmt.Sprintf(`stroke="%s"`, treadColor), `stroke-width="2"`, `fill="none"`)
	// CANVAS END
	canvas.End()

	svgString := svgBuffer.String()
	svgHtml := template.HTML(svgString)

	return svgHtml
}

func generateTyreWaveFillMiddleUpTemplateHTML(treadColor string, width float64, heightPercentage float64) template.HTML {
	var svgBuffer bytes.Buffer

	// PRE CALCULATION
	heightPercentage = math.Min(100, math.Max(0, heightPercentage))
	viewBoxHeight := 36.0
	fillHeight := (heightPercentage / 100.0) * viewBoxHeight
	fillYRight := viewBoxHeight - fillHeight

	slopeOffset := fillHeight * 0.3
	fillYLeft := math.Min(viewBoxHeight, fillYRight+slopeOffset)

	canvasHeight := 36.0 / 40.0 * width
	canvasViewBox := fmt.Sprintf(`viewBox="0 0 40 36"`)

	// CANVAS START
	canvas := svg.New(&svgBuffer)
	canvas.Start(int(width), int(canvasHeight), `fill="none"`, canvasViewBox, `id="fill-middle-full"`)
	// CANVAS CONTENT
	canvas.Def()
	canvas.ClipPath(`id="binClip2"`)
	canvas.Path("M1 8C1 4.13401 4.13401 1 8 1H32C35.866 1 39 4.13401 39 8V35H1V8Z")
	canvas.ClipEnd()
	canvas.DefEnd()
	pathD := fmt.Sprintf(`M0 %v L40 %v L40 36 L0 36 Z`, fillYLeft, fillYRight)
	canvas.Path(pathD, fmt.Sprintf(`fill="%s"`, treadColor), `clip-path="url(#binClip2)"`)
	canvas.Path("M1 8C1 4.13401 4.13401 1 8 1H32C35.866 1 39 4.13401 39 8V35H1V8Z", fmt.Sprintf(`stroke="%s"`, treadColor), `stroke-width="2"`, `fill="none"`)
	// CANVAS END
	canvas.End()

	svgString := svgBuffer.String()
	svgHtml := template.HTML(svgString)

	return svgHtml
}

func generateTyreWaveFillMiddleDownTemplateHTML(treadColor string, width float64, heightPercentage float64) template.HTML {
	var svgBuffer bytes.Buffer

	// PRE CALCULATION
	heightPercentage = math.Min(100, math.Max(0, heightPercentage))
	viewBoxHeight := 36.0
	fillHeight := (heightPercentage / 100.0) * viewBoxHeight
	fillYLeft := viewBoxHeight - fillHeight

	slopeOffset := fillHeight * 0.3
	fillYRight := math.Min(viewBoxHeight, fillYLeft+slopeOffset)

	canvasHeight := 36.0 / 40.0 * width
	canvasViewBox := fmt.Sprintf(`viewBox="0 0 40 36"`)

	// CANVAS START
	canvas := svg.New(&svgBuffer)
	canvas.Start(int(width), int(canvasHeight), `fill="none"`, canvasViewBox, `id="fill-middle-full"`)
	// CANVAS CONTENT
	canvas.Def()
	canvas.ClipPath(`id="binClip3"`)
	canvas.Path("M1 8C1 4.13401 4.13401 1 8 1H32C35.866 1 39 4.13401 39 8V35H1V8Z")
	canvas.ClipEnd()
	canvas.DefEnd()
	pathD := fmt.Sprintf(`M0 %v L40 %v L40 36 L0 36 Z`, fillYLeft, fillYRight)
	canvas.Path(pathD, fmt.Sprintf(`fill="%s"`, treadColor), `clip-path="url(#binClip3)"`)
	canvas.Path("M1 8C1 4.13401 4.13401 1 8 1H32C35.866 1 39 4.13401 39 8V35H1V8Z", fmt.Sprintf(`stroke="%s"`, treadColor), `stroke-width="2"`, `fill="none"`)
	// CANVAS END
	canvas.End()

	svgString := svgBuffer.String()
	svgHtml := template.HTML(svgString)

	return svgHtml
}

func generateTyreWaveFillMiddleFullTemplateHTML(treadColor string, width float64, heightPercentage float64) template.HTML {
	var svgBuffer bytes.Buffer

	// PRE CALCULATION
	heightPercentage = math.Min(100, math.Max(0, heightPercentage))
	viewBoxHeight := 36.0
	fillHeight := (heightPercentage / 100.0) * viewBoxHeight
	fillY := viewBoxHeight - fillHeight

	radius := 8.0
	if heightPercentage < 100 {
		radius = 8.0
	}

	canvasHeight := 36.0 / 40.0 * width
	canvasViewBox := fmt.Sprintf(`viewBox="0 0 40 36"`)

	// CANVAS START
	canvas := svg.New(&svgBuffer)
	canvas.Start(int(width), int(canvasHeight), `fill="none"`, canvasViewBox, `id="fill-middle-full"`)
	// CANVAS CONTENT
	pathD := fmt.Sprintf(`M 0 %v A %v %v 0 0 1 %v %v H %v A %v %v 0 0 1 40 %v V 36 H 0 Z`, (fillY + radius), radius, radius, radius, fillY, (40.0 - radius), radius, radius, (fillY + radius))
	canvas.Path(pathD, fmt.Sprintf(`fill="%s"`, treadColor))
	canvas.Path("M1 8C1 4.13401 4.13401 1 8 1H32C35.866 1 39 4.13401 39 8V35H1V8Z", fmt.Sprintf(`stroke="%s"`, treadColor), `stroke-width="2"`, `fill="none"`)
	// CANVAS END
	canvas.End()

	svgString := svgBuffer.String()
	svgHtml := template.HTML(svgString)

	return svgHtml
}

func generateTyreTreads(tyreWaveSource dtos.TyreWaveSource, scale float64, treadColor string) []dtos.TyreTread {
	var tyreTreads []dtos.TyreTread
	numberOfTread := tyreWaveSource.NoOfInspectionPoint + 1
	middleTreadSize := 17.0
	endTreadSize := 20.0

	prevRDTValueMap := map[int]float64{
		1: tyreWaveSource.RDT1,
		2: tyreWaveSource.RDT1,
		3: tyreWaveSource.RDT2,
		4: tyreWaveSource.RDT3,
		5: tyreWaveSource.RDT4,
	}

	for index := 1; index <= numberOfTread; index++ {
		isStart := false
		if index == 1 {
			isStart = true
		}
		isEnd := false
		if index == numberOfTread {
			isEnd = true
		}

		var svg template.HTML
		width := 0.0
		heightPercentage := 0.0
		value := 0.0
		prevValue := 0.0

		// VALUE
		if isEnd && index > 1 {
			switch tyreWaveSource.NoOfInspectionPoint {
			case 2:
				value = tyreWaveSource.RDT2
			case 3:
				value = tyreWaveSource.RDT3
			case 4:
				value = tyreWaveSource.RDT4
			default:
				value = tyreWaveSource.RDT4
			}
		} else if index == 1 {
			value = tyreWaveSource.RDT1
		} else if index == 2 {
			value = tyreWaveSource.RDT2
		} else if index == 3 {
			value = tyreWaveSource.RDT3
		} else if index == 4 {
			value = tyreWaveSource.RDT4
		}

		// PREV VALUE
		if index > 1 {
			prevValue = prevRDTValueMap[index]
		} else {
			prevValue = value
		}

		// SVG GENERATION
		if isStart {
			width = endTreadSize * scale
			heightPercentage = (value / tyreWaveSource.OTD) * 100.0
			svg = generateTyreWaveFillStartTemplateHTML(treadColor, width, heightPercentage)
		} else if isEnd {
			width = endTreadSize * scale
			heightPercentage = (value / tyreWaveSource.OTD) * 100.0
			svg = generateTyreWaveFillEndTemplateHTML(treadColor, width, heightPercentage)
		} else {
			width = middleTreadSize * scale
			heightPercentage = (value / tyreWaveSource.OTD) * 100.0
			if value > prevValue {
				svg = generateTyreWaveFillMiddleUpTemplateHTML(treadColor, width, heightPercentage)
			} else if value < prevValue {
				svg = generateTyreWaveFillMiddleDownTemplateHTML(treadColor, width, heightPercentage)
			} else {
				svg = generateTyreWaveFillMiddleFullTemplateHTML(treadColor, width, heightPercentage)
			}
		}

		tyreTreads = append(tyreTreads, dtos.TyreTread{
			Svg:              svg,
			Color:            treadColor,
			HeightPercentage: heightPercentage,
			Width:            width,
			Value:            value,
		})
	}

	return tyreTreads
}

func (u *AssetInspectionUtil) GenerateTyreWaveSvg(tyreWaveSource dtos.TyreWaveSource) dtos.TyreWaveSvgResponse {
	gap := 3.0
	scale := 2.0

	var tyreWaveWidth float64
	var treadColor string

	// TYRE WAVE WIDTH
	switch tyreWaveSource.NoOfInspectionPoint {
	case 2:
		tyreWaveWidth = (60.0 + gap) * scale
	case 3:
		tyreWaveWidth = (80.0 + gap) * scale
	case 4:
		tyreWaveWidth = (100.0 + gap) * scale
	default:
		tyreWaveWidth = (100.0 + gap) * scale
	}

	// TREAD COLOR
	averageRTD := helpers.CalculateAverageRTD(tyreWaveSource.RDT1, tyreWaveSource.RDT2, tyreWaveSource.RDT3, tyreWaveSource.RDT4)
	TUR := helpers.CalculateTyreUtilRate(tyreWaveSource.OTD, averageRTD)

	if tyreWaveSource.ShowOTD {
		if tyreWaveSource.OTD == averageRTD && averageRTD > 0 {
			treadColor = "#8CD999"
		} else if TUR < 1 || TUR == 0 {
			treadColor = "#BABCC3"
		} else if TUR <= 30 {
			treadColor = "#8CD999"
		} else if TUR <= 60 {
			treadColor = "#F6E658"
		} else if TUR <= 80 {
			treadColor = "#FFAB49"
		} else {
			treadColor = "#FE6E71"
		}
	} else {
		nonZeroRTDs := 0
		if tyreWaveSource.RDT1 > 0 {
			nonZeroRTDs++
		}
		if tyreWaveSource.RDT2 > 0 {
			nonZeroRTDs++
		}
		if tyreWaveSource.RDT3 > 0 {
			nonZeroRTDs++
		}
		if tyreWaveSource.RDT4 > 0 {
			nonZeroRTDs++
		}

		if nonZeroRTDs == 0 {
			treadColor = "#BABCC3"
		} else if averageRTD > 5 {
			treadColor = "#185FFF"
		} else if averageRTD >= 3 {
			treadColor = "#FFAB49"
		} else {
			treadColor = "#FE6E71"
		}
	}

	// TYRE WAVE BASE
	var tyreBaseTemplateHTML template.HTML
	switch tyreWaveSource.NoOfInspectionPoint {
	case 2:
		tyreBaseTemplateHTML = generateTyreWaveBase2TemplateHTML(treadColor, tyreWaveWidth)
	case 3:
		tyreBaseTemplateHTML = generateTyreWaveBase3TemplateHTML(treadColor, tyreWaveWidth)
	case 4:
		tyreBaseTemplateHTML = generateTyreWaveBase4TemplateHTML(treadColor, tyreWaveWidth)
	default:
		tyreBaseTemplateHTML = generateTyreWaveBase4TemplateHTML(treadColor, tyreWaveWidth)
	}

	// TYRE TREADS
	tyreTreads := generateTyreTreads(tyreWaveSource, scale, treadColor)

	// TREAD DEPTHS
	var treadDepths []float64
	treadDepths = append(treadDepths, tyreWaveSource.RDT1)
	treadDepths = append(treadDepths, tyreWaveSource.RDT2)
	if tyreWaveSource.NoOfInspectionPoint >= 3 {
		treadDepths = append(treadDepths, tyreWaveSource.RDT3)
	}
	if tyreWaveSource.NoOfInspectionPoint >= 4 {
		treadDepths = append(treadDepths, tyreWaveSource.RDT4)
	}

	// INSPECTION POINTS
	var inspectionPoints []int
	for index := 1; index <= tyreWaveSource.NoOfInspectionPoint; index++ {
		inspectionPoints = append(inspectionPoints, index)
	}

	// OTD LABEL
	OTDLabel := "-"
	if tyreWaveSource.OTD > 0 {
		OTDLabel = fmt.Sprintf("%.1f", tyreWaveSource.OTD)
	}

	// RESPONSE
	response := dtos.TyreWaveSvgResponse{
		TyreWaveWidth:       tyreWaveWidth,
		TreadDepths:         treadDepths,
		TyreTreads:          tyreTreads,
		BaseTread:           tyreBaseTemplateHTML,
		OTDLabel:            OTDLabel,
		ShowOTD:             tyreWaveSource.ShowOTD,
		InspectionPoints:    inspectionPoints,
		NoOfInspectionPoint: tyreWaveSource.NoOfInspectionPoint,
	}

	return response
}

func (u *AssetInspectionUtil) CalculateAxleConfigurationZoom(axleDataLength int) float64 {
	floatAxleDataLength := float64(axleDataLength)
	zoomValue := 0.01*floatAxleDataLength*floatAxleDataLength - 0.245*floatAxleDataLength + 1.925

	if axleDataLength >= 15 {
		zoomValue = zoomValue * 0.75
	}

	return zoomValue
}
