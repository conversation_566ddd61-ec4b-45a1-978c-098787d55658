package dtos

import (
	"assetfindr/internal/app/content/models"
	"time"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
)

type GetFormReq struct {
	ReferenceID      string `form:"reference_id" binding:"required"`
	FormCategoryCode string `form:"type" binding:"required"`
}

type Form struct {
	ID               string      `json:"id"`
	ReferenceID      string      `json:"reference_id"`
	FormCategoryCode string      `json:"form_category_code"`
	FormFields       []FormField `json:"form_fields"`
	CreatedAt        time.Time   `json:"created_at"`
	StatusCode       string      `json:"status_code"`
	TemplateID       null.String `json:"template_id"`
}

func BuildFormResp(form *models.Form) *Form {
	if form == nil {
		return nil
	}
	resp := Form{
		ID:               form.ID,
		ReferenceID:      form.ReferenceID,
		FormCategoryCode: form.FormCategoryCode,
		FormFields:       make([]FormField, 0, len(form.FormFields)),
		CreatedAt:        form.CreatedAt,
		StatusCode:       form.StatusCode,
		TemplateID:       form.TemplateID,
	}

	for i := range form.FormFields {
		resp.FormFields = append(resp.FormFields, BuildFormFieldResp(form.FormFields[i]))
	}

	return &resp
}

type FormField struct {
	ID                string       `json:"id,omitempty"`
	FormFieldTypeCode string       `json:"field_type_code"`
	Options           pgtype.JSONB `json:"options" binding:"required"`
	Sequence          int          `json:"sequence"`
	Label             string       `json:"label"`
	Value             pgtype.JSONB `json:"value,omitempty"`
	IsMandatory       null.Bool    `json:"is_mandatory"`
	TemplateFieldID   null.String  `json:"template_field_id"`
}

func BuildFormFieldResp(form models.FormField) FormField {
	if form.Options.Status == pgtype.Undefined {
		form.Options.Status = pgtype.Null
	}

	if form.Value.Status == pgtype.Undefined {
		form.Value.Status = pgtype.Null
	}
	return FormField{
		ID:                form.ID,
		FormFieldTypeCode: form.FormFieldTypeCode,
		Options:           form.Options,
		Sequence:          form.Sequence,
		Label:             form.Label,
		Value:             form.Value,
		IsMandatory:       form.IsMandatory,
	}
}

func BuildFormTemplateFieldResp(formTemplate models.FormTemplateField) FormField {
	if formTemplate.Options.Status == pgtype.Undefined {
		formTemplate.Options.Status = pgtype.Null
	}

	if formTemplate.Value.Status == pgtype.Undefined {
		formTemplate.Value.Status = pgtype.Null
	}
	return FormField{
		ID:                formTemplate.ID,
		FormFieldTypeCode: formTemplate.FormFieldTypeCode,
		Options:           formTemplate.Options,
		Sequence:          formTemplate.Sequence,
		Label:             formTemplate.Label,
		Value:             formTemplate.Value,
		IsMandatory:       formTemplate.IsMandatory,
	}
}

type PrintHTMLForm struct {
	ID               string               `json:"id"`
	ReferenceID      string               `json:"reference_id"`
	FormCategoryCode string               `json:"form_category_code"`
	FormFields       []PrintHTMLFormField `json:"form_fields"`
	CreatedAt        time.Time            `json:"created_at"`
	StatusCode       string               `json:"status_code"`
}

type PrintHTMLFormField struct {
	ID                string                           `json:"id,omitempty"`
	FormFieldTypeCode string                           `json:"field_type_code"`
	Options           PrintHTMLFormFieldOptOrValueData `json:"options" binding:"required"`
	Sequence          int                              `json:"sequence"`
	Label             string                           `json:"label"`
	Value             PrintHTMLFormFieldOptOrValueData `json:"value,omitempty"`
	IsMandatory       null.Bool                        `json:"is_mandatory"`
}

type PrintHTMLFormFieldOptOrValueData struct {
	Data interface{}
}
