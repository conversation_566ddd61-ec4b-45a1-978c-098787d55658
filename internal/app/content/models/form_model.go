package models

import (
	"assetfindr/pkg/common/commonmodel"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

// Form model
type Form struct {
	commonmodel.ModelV2
	ReferenceID      string
	FormCategoryCode string
	FormFields       []FormField
	StatusCode       string `gorm:"default:null"`
	TemplateID       null.String
}

func (Form) TableName() string {
	return "ctn_forms"
}

func (f *Form) BeforeCreate(db *gorm.DB) error {
	f.SetUUID("frm")
	f.ModelV2.BeforeCreate(db)
	return nil
}

func (f *Form) BeforeUpdate(db *gorm.DB) error {
	f.ModelV2.BeforeUpdate(db)
	return nil
}

// FormField model
type FormField struct {
	commonmodel.ModelV2
	FormID            string
	FormFieldTypeCode string
	Options           pgtype.JSONB
	Sequence          int
	Label             string
	Value             pgtype.JSONB
	IsMandatory       null.Bool
	TemplateFieldID   null.String
}

func (FormField) TableName() string {
	return "ctn_form_fields"
}

func (ff *FormField) BeforeCreate(db *gorm.DB) error {
	ff.SetUUID("fld")
	ff.ModelV2.BeforeCreate(db)
	return nil
}

func (ff *FormField) BeforeUpdate(db *gorm.DB) error {
	ff.ModelV2.BeforeUpdate(db)
	return nil
}

type FormWhere struct {
	ReferenceID      string
	ReferenceIDs     []string
	TemplateID       string
	FormCategoryCode string
	ClientID         string
}

type FormCondition struct {
	Where       FormWhere
	Columns     []string
	Preload     FormPreload
	IsForUpdate bool
}

type FormPreload struct {
	FormFields bool
}
