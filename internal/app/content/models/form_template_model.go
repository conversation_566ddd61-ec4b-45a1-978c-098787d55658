package models

import (
	"assetfindr/pkg/common/commonmodel"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

// FormTemplate model
type FormTemplate struct {
	commonmodel.ModelV2
	Name             string
	FormCategoryCode string
	FormFields       []FormTemplateField
	IsDefault        null.Bool
}

func (FormTemplate) TableName() string {
	return "ctn_form_templates"
}

func (f *FormTemplate) BeforeCreate(db *gorm.DB) error {
	f.SetUUID("tml")
	f.ModelV2.BeforeCreate(db)
	return nil
}

func (f *FormTemplate) BeforeUpdate(db *gorm.DB) error {
	f.ModelV2.BeforeUpdate(db)
	return nil
}

// FormTemplateField model
type FormTemplateField struct {
	commonmodel.ModelV2
	FormTemplateID    string
	FormFieldTypeCode string
	Options           pgtype.JSONB
	Sequence          int
	Label             string
	Value             pgtype.JSONB
	IsMandatory       null.Bool
}

func (FormTemplateField) TableName() string {
	return "ctn_form_template_fields"
}

func (tf *FormTemplateField) BeforeCreate(db *gorm.DB) error {
	tf.SetUUID("tmf")
	tf.ModelV2.BeforeCreate(db)
	return nil
}

func (tf *FormTemplateField) BeforeUpdate(db *gorm.DB) error {
	tf.ModelV2.BeforeUpdate(db)
	return nil
}

type FormTemplateWhere struct {
	ID               string
	FormCategoryCode string
	ClientID         string
	WithOrmDeleted   bool
}

type FormTemplateCondition struct {
	Where       FormTemplateWhere
	Columns     []string
	Preload     FormTemplatePreload
	IsForUpdate bool
}

type FormTemplatePreload struct {
	FormFields bool
}

type GetFormTemplateListParam struct {
	commonmodel.ListRequest
	Cond FormTemplateCondition
}
