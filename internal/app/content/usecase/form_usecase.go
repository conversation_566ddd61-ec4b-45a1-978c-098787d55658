package usecase

import (
	"assetfindr/internal/app/content/constants"
	"assetfindr/internal/app/content/dtos"
	"assetfindr/internal/app/content/models"
	"assetfindr/internal/app/content/repository"
	ticketModels "assetfindr/internal/app/task/models"
	ticketRepo "assetfindr/internal/app/task/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/pgtypehelpers"
	"context"
	"fmt"
)

type FormUseCase struct {
	DB         database.DBUsecase
	formRepo   repository.FormRepository
	ticketRepo ticketRepo.TicketRepository
}

func NewFormUsecase(
	DB database.DBUsecase,
	contentRepo repository.FormRepository,
	ticketRepo ticketRepo.TicketRepository,
) FormUseCase {
	return FormUseCase{
		DB:         DB,
		formRepo:   contentRepo,
		ticketRepo: ticketRepo,
	}
}

func (uc *FormUseCase) CreateForm(ctx context.Context, req dtos.FormReq) (*commonmodel.CreateResponse, error) {
	form := &models.Form{
		ReferenceID:      req.ReferenceID,
		FormCategoryCode: req.FormCategoryCode,
		StatusCode:       req.StatusCode,
		TemplateID:       req.TemplateID,
	}

	for _, field := range req.Fields {
		form.FormFields = append(form.FormFields, models.FormField{
			FormFieldTypeCode: field.FieldTypeCode,
			Options:           field.Options,
			Sequence:          field.Sequence,
			Label:             field.Label,
			Value:             field.Value,
			IsMandatory:       field.IsMandatory,
			TemplateFieldID:   field.TemplateFieldID,
		})
	}
	err := uc.formRepo.CreateForm(ctx, uc.DB.WithCtx(ctx).DB(), form)
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: form.ID,
		Data:        nil,
	}, nil
}

func (uc *FormUseCase) UpsertForm(ctx context.Context, req commonmodel.FormReq) (*commonmodel.UpdateResponse, error) {
	form := &models.Form{
		ReferenceID:      req.ReferenceID,
		FormCategoryCode: req.FormCategoryCode,
		StatusCode:       req.StatusCode,
		TemplateID:       req.TemplateID,
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	deleteFieldIDs := []string{}
	for _, field := range req.Fields {
		switch {
		case field.IsNew():
			form.FormFields = append(form.FormFields, models.FormField{
				FormFieldTypeCode: field.FieldTypeCode,
				Options:           field.Options,
				Sequence:          field.Sequence,
				Label:             field.Label,
				Value:             field.Value,
				IsMandatory:       field.IsMandatory,
				TemplateFieldID:   field.TemplateFieldID,
			})
		case field.IsDelete:
			deleteFieldIDs = append(deleteFieldIDs, field.ID)
		default:
			err = uc.formRepo.UpdateFormField(ctx, tx.DB(), field.ID,
				&models.FormField{
					FormFieldTypeCode: field.FieldTypeCode,
					Options:           field.Options,
					Sequence:          field.Sequence,
					Label:             field.Label,
					Value:             field.Value,
					IsMandatory:       field.IsMandatory,
					TemplateFieldID:   field.TemplateFieldID,
				},
			)
			if err != nil {
				return nil, err
			}
		}
	}

	err = uc.formRepo.DeleteFormFieldByIDs(ctx, tx.DB(), deleteFieldIDs)
	if err != nil {
		return nil, err
	}

	err = uc.formRepo.UpsertForm(ctx, tx.DB(), form)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: form.ID,
		Data:        nil,
	}, nil
}

func (uc *FormUseCase) CreateFormTemplate(ctx context.Context, req dtos.TemplateReq) (*commonmodel.CreateResponse, error) {
	template := &models.FormTemplate{
		Name:             req.Name,
		FormCategoryCode: req.FormCategoryCode,
		IsDefault:        req.IsDefault,
	}

	for _, field := range req.Fields {
		template.FormFields = append(template.FormFields, models.FormTemplateField{
			FormFieldTypeCode: field.FieldTypeCode,
			Options:           field.Options,
			Sequence:          field.Sequence,
			Label:             field.Label,
			Value:             field.Value,
			IsMandatory:       field.IsMandatory,
		})
	}
	err := uc.formRepo.CreateFormTemplate(ctx, uc.DB.WithCtx(ctx).DB(), template)
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: template.ID,
		Data:        nil,
	}, nil
}

func (uc *FormUseCase) UpdateFormTemplate(ctx context.Context, id string, req dtos.TemplateReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	_, err = uc.formRepo.GetFormTemplate(ctx, uc.DB.DB(), models.FormTemplateCondition{
		Where: models.FormTemplateWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.FormTemplatePreload{
			FormFields: true,
		},
	})
	if err != nil {
		return nil, err
	}

	template := &models.FormTemplate{
		Name:             req.Name,
		FormCategoryCode: req.FormCategoryCode,
		IsDefault:        req.IsDefault,
	}

	deleteFieldIDs := []string{}
	newFields := []models.FormTemplateField{}
	for _, field := range req.Fields {
		switch {
		case field.IsNew():
			newFields = append(newFields, models.FormTemplateField{
				FormTemplateID:    id,
				FormFieldTypeCode: field.FieldTypeCode,
				Options:           field.Options,
				Sequence:          field.Sequence,
				Label:             field.Label,
				Value:             field.Value,
				IsMandatory:       field.IsMandatory,
			})
		case field.IsDelete:
			deleteFieldIDs = append(deleteFieldIDs, field.ID)
		default:
			err := uc.formRepo.UpdateFormTemplateField(ctx, tx.DB(), field.ID,
				&models.FormTemplateField{
					FormFieldTypeCode: field.FieldTypeCode,
					Options:           field.Options,
					Sequence:          field.Sequence,
					Label:             field.Label,
					Value:             field.Value,
					IsMandatory:       field.IsMandatory,
				},
			)
			if err != nil {
				return nil, err
			}
		}
	}

	err = uc.formRepo.CreateFormTemplateFields(ctx, tx.DB(), newFields)
	if err != nil {
		return nil, err
	}

	err = uc.formRepo.UpdateFormTemplate(ctx, tx.DB(), id, template)
	if err != nil {
		return nil, err
	}

	err = uc.formRepo.DeleteFormTemplateFieldByIDs(ctx, tx.DB(), deleteFieldIDs)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *FormUseCase) GetForm(ctx context.Context, req dtos.GetFormReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	form, err := uc.formRepo.GetForm(ctx, uc.DB.DB(), models.FormCondition{
		Where: models.FormWhere{
			ReferenceID:      req.ReferenceID,
			FormCategoryCode: req.FormCategoryCode,
			ClientID:         claim.GetLoggedInClientID(),
		},
		Preload: models.FormPreload{
			FormFields: true,
		},
	})
	if err != nil {
		return nil, err
	}

	resp := dtos.Form{
		ID:               form.ID,
		ReferenceID:      form.ReferenceID,
		FormCategoryCode: form.FormCategoryCode,
		FormFields:       make([]dtos.FormField, 0, len(form.FormFields)),
		CreatedAt:        form.CreatedAt,
		StatusCode:       form.StatusCode,
		TemplateID:       form.TemplateID,
	}
	for _, field := range form.FormFields {
		resp.FormFields = append(resp.FormFields, dtos.FormField{
			ID:                field.ID,
			FormFieldTypeCode: field.FormFieldTypeCode,
			Options:           pgtypehelpers.HandleValue(field.Options),
			Sequence:          field.Sequence,
			Label:             field.Label,
			Value:             pgtypehelpers.HandleValue(field.Value),
			IsMandatory:       field.IsMandatory,
			TemplateFieldID:   field.TemplateFieldID,
		})
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: form.ID,
		Data:        resp,
	}, nil
}

func (uc *FormUseCase) GetFormTemplate(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tmpl, err := uc.formRepo.GetFormTemplate(ctx, uc.DB.DB(), models.FormTemplateCondition{
		Where: models.FormTemplateWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.FormTemplatePreload{
			FormFields: true,
		},
	})
	if err != nil {
		return nil, err
	}

	resp := dtos.FormTemplate{
		ID:               tmpl.ID,
		Name:             tmpl.Name,
		FormCategoryCode: tmpl.FormCategoryCode,
		FormFields:       make([]dtos.FormField, 0, len(tmpl.FormFields)),
		IsDefault:        tmpl.IsDefault,
	}
	for _, field := range tmpl.FormFields {
		resp.FormFields = append(resp.FormFields, dtos.FormField{
			ID:                field.ID,
			FormFieldTypeCode: field.FormFieldTypeCode,
			Options:           pgtypehelpers.HandleValue(field.Options),
			Sequence:          field.Sequence,
			Label:             field.Label,
			Value:             pgtypehelpers.HandleValue(field.Value),
			IsMandatory:       field.IsMandatory,
		})
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: tmpl.ID,
		Data:        resp,
	}, nil
}

func (uc *FormUseCase) GetFormTemplateList(ctx context.Context, req dtos.FromTemplateListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	count, tmpls, err := uc.formRepo.GetFormTemplateList(ctx, uc.DB.DB(), models.GetFormTemplateListParam{
		ListRequest: req.ListRequest,
		Cond: models.FormTemplateCondition{
			Where: models.FormTemplateWhere{
				FormCategoryCode: req.Type,
				ClientID:         claim.GetLoggedInClientID(),
			},
		},
	})
	if err != nil {
		return nil, err
	}

	resp := make([]dtos.FormTemplate, 0, len(tmpls))
	for _, tmpl := range tmpls {
		resp = append(resp, dtos.FormTemplate{
			ID:               tmpl.ID,
			Name:             tmpl.Name,
			FormCategoryCode: tmpl.FormCategoryCode,
			IsDefault:        tmpl.IsDefault,
		})
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         resp,
	}, nil
}

func (uc *FormUseCase) UpsertFlows(ctx context.Context, req []dtos.FlowReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	deleteFlowIDs := []string{}
	newFlows := []models.Flow{}
	for _, flow := range req {
		switch {
		case flow.IsNew():
			if flow.FlowTypeCode == constants.FLOW_TYPE_CUSTOM_FORM && flow.FlowTypeID.String == "" {
				return nil, errorhandler.ErrBadRequest("flow_type_id is required on flow_type_code CUSTOM_FORM")
			}
			newFlows = append(newFlows, models.Flow{
				ReferenceID:   flow.ReferenceID,
				ReferenceCode: flow.ReferenceCode,
				FlowTypeID:    flow.FlowTypeID,
				FlowTypeCode:  flow.FlowTypeCode,
			})
		case flow.IsDeleted:
			deleteFlowIDs = append(deleteFlowIDs, flow.ID)
		default:
			exsistingFlow, err := uc.formRepo.GetFlow(ctx, tx.DB(), models.FlowCondition{
				Where: models.FlowWhere{
					ID:       flow.ID,
					ClientID: claim.GetLoggedInClientID(),
				},
			})
			if err != nil {
				return nil, err
			}

			if exsistingFlow.FlowTypeID != flow.FlowTypeID {
				form, err := uc.formRepo.GetForm(ctx, tx.DB(), models.FormCondition{
					Where: models.FormWhere{
						ReferenceID: flow.ID,
					},
				})
				if err == nil {
					err = uc.formRepo.DeleteFormByIDs(ctx, tx.DB(), form.ID)
					if err != nil {
						return nil, err
					}

					err = uc.formRepo.DeleteFormFeildsByFormIDs(ctx, tx.DB(), form.ID)
					if err != nil {
						return nil, err
					}
				}
			}
			err = uc.formRepo.UpdateFlow(ctx, tx.DB(), flow.ID,
				&models.Flow{
					ReferenceID:   flow.ReferenceID,
					ReferenceCode: flow.ReferenceCode,
					FlowTypeID:    flow.FlowTypeID,
					FlowTypeCode:  flow.FlowTypeCode,
				},
			)
			if err != nil {
				return nil, err
			}
		}
	}

	if len(deleteFlowIDs) > 0 {
		forms, err := uc.formRepo.GetForms(ctx, tx.DB(), models.FormCondition{
			Where: models.FormWhere{
				ReferenceIDs: deleteFlowIDs,
			},
		})
		if err != nil {
			return nil, err
		}

		formIDs := make([]string, 0, len(forms))
		for i := range forms {
			formIDs = append(formIDs, forms[i].ID)
		}

		err = uc.formRepo.DeleteFormByIDs(ctx, tx.DB(), formIDs...)
		if err != nil {
			return nil, err
		}

		err = uc.formRepo.DeleteFormFeildsByFormIDs(ctx, tx.DB(), formIDs...)
		if err != nil {
			return nil, err
		}
	}

	err = uc.formRepo.DeleteFlows(ctx, tx.DB(), deleteFlowIDs)
	if err != nil {
		return nil, err
	}

	err = uc.formRepo.CreateFlows(ctx, tx.DB(), newFlows)
	if err != nil {
		return nil, err
	}

	ticketNote := ticketModels.TicketNote{
		TicketID: req[0].ReferenceID,
		UserID:   claim.UserID,
		Title:    fmt.Sprintf("%s added new template(s) to Work Order Flow.", claim.GetName()),
		Notes:    "",
		ClientID: claim.GetLoggedInClientID(),
	}

	err = uc.ticketRepo.CreateNote(ctx, tx.DB(), &ticketNote)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        nil,
	}, nil
}

func (uc *FormUseCase) GetFlows(ctx context.Context, req dtos.FlowListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	if req.ReferenceID == "" {
		return nil, errorhandler.ErrBadRequest("reference_id is required in url param")
	}

	flows, err := uc.formRepo.GetFlows(ctx, uc.DB.DB(), models.FlowCondition{
		Where: models.FlowWhere{
			ReferenceID:   req.ReferenceID,
			ReferenceCode: req.ReferenceCode,
			ClientID:      claim.GetLoggedInClientID(),
		},
		Preload: models.FlowPreload{
			FormTemplate: true,
			Form:         true,
		},
	})
	if err != nil {
		return nil, err
	}

	resp := make([]dtos.Flow, 0, len(flows))
	for _, tmpl := range flows {
		resp = append(resp, dtos.Flow{
			ID:            tmpl.ID,
			ReferenceID:   tmpl.ReferenceID,
			ReferenceCode: tmpl.ReferenceCode,
			FlowTypeID:    tmpl.FlowTypeID,
			FlowTypeCode:  tmpl.FlowTypeCode,
			FormTemplate:  dtos.BuildFormTemplateResp(tmpl.FormTemplate),
			Form:          dtos.BuildFormResp(tmpl.Form),
		})
	}

	return &commonmodel.ListResponse{
		TotalRecords: len(flows),
		PageSize:     len(flows),
		PageNo:       1,
		Data:         resp,
	}, nil
}
