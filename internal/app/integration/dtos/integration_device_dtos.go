package dtos

import (
	geoModel "assetfindr/internal/app/geo/models"
	"assetfindr/internal/app/integration/models"
	userModel "assetfindr/internal/app/user-identity/models"
	"assetfindr/pkg/common/commonmodel"
	"time"

	"gopkg.in/guregu/null.v4"
)

type IntegrationDeviceListReq struct {
	commonmodel.ListRequest
	ClientID                string   `form:"client_id"`
	SerialOrReferenceNumber string   `form:"serial_or_reference_number"`
	IntegrationID           string   `form:"integration_id"`
	IntegrationIDs          []string `form:"integration_ids"`
	IntegrationTypeCode     string   `form:"integration_type_code"`
	IntegrationTypeCodes    []string `form:"integration_type_codes"`
	ReferenceCode           string   `form:"reference_code"`
	ReferenceCodes          []string `form:"reference_codes"`
	AssetID                 string   `form:"asset_id"`
	AssetIDs                []string `form:"asset_ids"`
	AttachedByUserID        string   `form:"attached_by_user_id"`
	AttachedByUserIDs       []string `form:"attached_by_user_ids"`
	AttachedAtStart         string   `form:"attached_at_start"`
	AttachedAtEnd           string   `form:"attached_at_end"`
}

type IntegrationDeviceHistoryListReq struct {
	commonmodel.ListRequest
	ClientID             string   `form:"client_id"`
	IntegrationID        string   `form:"integration_id"`
	IntegrationIDs       []string `form:"integration_ids"`
	IntegrationDeviceID  string   `form:"integration_device_id"`
	IntegrationDeviceIDs []string `form:"integration_device_ids"`
	AttachedByUserID     string   `form:"attached_by_user_id"`
	AttachedByUserIDs    []string `form:"attached_by_user_ids"`
	AttachedAtStart      string   `form:"attached_at_start"`
	AttachedAtEnd        string   `form:"attached_at_end"`
	DetachedByUserID     string   `form:"detached_by_user_id"`
	DetachedByUserIDs    []string `form:"detached_by_user_ids"`
	DetachedAtStart      string   `form:"detached_at_start"`
	DetachedAtEnd        string   `form:"detached_at_end"`
}

type IntegrationLinkedTyre struct {
	ID              string `json:"id"`
	AssetName       string `json:"asset_name"`
	SerialNumber    string `json:"serial_number"`
	ReferenceNumber string `json:"reference_number"`
}

type IntegrationLinkedVehicle struct {
	ID              string `json:"id"`
	AssetName       string `json:"asset_name"`
	SerialNumber    string `json:"serial_number"`
	ReferenceNumber string `json:"reference_number"`
	TyrePosition    int    `json:"tyre_position"`
}

type IntegrationDevice struct {
	ID                             string                   `json:"id"`
	IntegrationTypeCode            string                   `json:"integration_type_code"`
	ReferenceCode                  string                   `json:"reference_code"`
	IntegrationID                  *string                  `json:"integration_id"`
	AttachedAt                     null.Time                `json:"attached_at"`
	AttachedByUserID               *string                  `json:"attached_by_user_id"`
	LinkedTyre                     IntegrationLinkedTyre    `json:"linked_tyre"`
	LinkedVehicle                  IntegrationLinkedVehicle `json:"linked_vehicle"`
	LatestIntegrationDataUpdatedAt null.Time                `json:"latest_integration_data_updated_at"`
	LatestIntegrationDataJSON      any                      `json:"latest_integration_data_json"`
	ClientID                       string                   `json:"client_id"`
	ClientAlias                    string                   `json:"client_alias"`
	ClientName                     string                   `json:"client_name"`
}

func (i *IntegrationDevice) Set(
	integrationDevice models.IntegrationDevice,
	integrationDeviceIntegrationMap map[string]geoModel.LatestIntegrationData,
	clientMapByID map[string]userModel.Client,
) {
	i.ID = integrationDevice.ID
	i.IntegrationTypeCode = integrationDevice.IntegrationTypeCode
	i.ReferenceCode = integrationDevice.ReferenceCode
	i.IntegrationID = integrationDevice.IntegrationID
	i.AttachedAt = integrationDevice.AttachedAt
	i.AttachedByUserID = integrationDevice.AttachedByUserID

	// Safe check before dereferencing pointer
	if integrationDevice.IntegrationID != nil {
		if data, ok := integrationDeviceIntegrationMap[*integrationDevice.IntegrationID]; ok {
			i.LatestIntegrationDataUpdatedAt = null.TimeFrom(data.UpdatedAt)
			i.LatestIntegrationDataJSON = data.DataJSON
		}
	}

	// Guard Integration and AssetDetail
	if integrationDevice.Integration.ID != "" &&

		integrationDevice.Integration.AssetDetail.ID != "" {

		i.LinkedTyre = IntegrationLinkedTyre{
			ID:              integrationDevice.Integration.AssetDetail.ID,
			AssetName:       integrationDevice.Integration.AssetDetail.Name,
			SerialNumber:    integrationDevice.Integration.AssetDetail.SerialNumber,
			ReferenceNumber: integrationDevice.Integration.AssetDetail.ReferenceNumber,
		}

		assetTyre := integrationDevice.Integration.AssetDetail.AssetTyre
		if assetTyre != nil &&
			assetTyre.AssetID != "" &&
			assetTyre.AssetLinked != nil &&
			assetTyre.AssetLinked.ID != "" &&
			assetTyre.AssetLinked.AssetParent.ID != "" {

			i.LinkedVehicle = IntegrationLinkedVehicle{
				ID:              assetTyre.AssetLinked.AssetParent.ID,
				AssetName:       assetTyre.AssetLinked.AssetParent.Name,
				SerialNumber:    assetTyre.AssetLinked.AssetParent.SerialNumber,
				ReferenceNumber: assetTyre.AssetLinked.AssetParent.ReferenceNumber,
			}

			if assetTyre.AssetLinked.AssetLinkedAssetVehicleTyre != nil {
				i.LinkedVehicle.TyrePosition = assetTyre.AssetLinked.AssetLinkedAssetVehicleTyre.TyrePosition
			}
		}
	}

	// Client mapping
	i.ClientID = integrationDevice.ClientID
	if data, ok := clientMapByID[integrationDevice.ClientID]; ok {
		i.ClientAlias = data.ClientAlias
		i.ClientName = data.Name
	}
}

type IntegrationDeviceCreateUpdateReq struct {
	ReferenceCode string `json:"reference_code"`
	ClientID      string `json:"client_id"`
	AssetTyreID   string `json:"asset_tyre_id"`
}

type IntegrationDeviceHistory struct {
	ID                     string                   `json:"id"`
	IntegrationID          string                   `json:"integration_id"`
	IntegrationDeviceID    string                   `json:"integration_device_id"`
	AttachedAt             time.Time                `json:"attached_at"`
	AttachedByUserID       string                   `json:"attached_by_user_id"`
	AttachedByUserFullName string                   `json:"attached_by_user_full_name"`
	DetachedAt             null.Time                `json:"detached_at"`
	DetachedByUserID       string                   `json:"detached_by_user_id"`
	DetachedByUserFullName string                   `json:"detached_by_user_full_name"`
	ClientID               string                   `json:"client_id"`
	ClientName             string                   `json:"client_name"`
	ClientAlias            string                   `json:"client_alias"`
	ReferenceCode          string                   `json:"reference_code"`
	LinkedTyre             IntegrationLinkedTyre    `json:"linked_tyre"`
	LinkedVehicle          IntegrationLinkedVehicle `json:"linked_vehicle"`
}

func (i *IntegrationDeviceHistory) Set(
	history models.IntegrationDeviceHistory,
	userMapByID map[string]userModel.User,
	clientMapByID map[string]userModel.Client,
) {
	i.ID = history.ID
	i.IntegrationID = history.IntegrationID
	i.IntegrationDeviceID = history.IntegrationDeviceID
	i.AttachedAt = history.AttachedAt
	i.AttachedByUserID = history.AttachedByUserID

	// AttachedBy user
	if data, ok := userMapByID[history.AttachedByUserID]; ok {
		i.AttachedByUserFullName = data.GetName()
	}

	i.DetachedAt = history.DetachedAt
	i.DetachedByUserID = history.DetachedByUserID

	// DetachedBy user
	if history.DetachedByUserID != "" {
		if data, ok := userMapByID[history.DetachedByUserID]; ok {
			i.DetachedByUserFullName = data.GetName()
		}
	}

	i.ClientID = history.ClientID
	if client, ok := clientMapByID[history.ClientID]; ok {
		i.ClientAlias = client.ClientAlias
		i.ClientName = client.Name
	}

	// Guard: IntegrationDevice may be nil
	if history.IntegrationDevice.ID != "" {
		i.ReferenceCode = history.IntegrationDevice.ReferenceCode
	}

	// Guard: Integration may be nil
	if history.Integration.ID != "" &&

		history.Integration.AssetDetail.ID != "" {

		i.LinkedTyre = IntegrationLinkedTyre{
			ID:              history.Integration.AssetDetail.ID,
			AssetName:       history.Integration.AssetDetail.Name,
			SerialNumber:    history.Integration.AssetDetail.SerialNumber,
			ReferenceNumber: history.Integration.AssetDetail.ReferenceNumber,
		}

		assetTyre := history.Integration.AssetDetail.AssetTyre
		if assetTyre != nil &&
			assetTyre.AssetID != "" &&
			assetTyre.AssetLinked != nil &&
			assetTyre.AssetLinked.AssetParent.ID != "" {

			i.LinkedVehicle = IntegrationLinkedVehicle{
				ID:              assetTyre.AssetLinked.AssetParent.ID,
				AssetName:       assetTyre.AssetLinked.AssetParent.Name,
				SerialNumber:    assetTyre.AssetLinked.AssetParent.SerialNumber,
				ReferenceNumber: assetTyre.AssetLinked.AssetParent.ReferenceNumber,
			}

			// Guard TyrePosition
			if assetTyre.AssetLinked.AssetLinkedAssetVehicleTyre != nil {
				i.LinkedVehicle.TyrePosition = assetTyre.AssetLinked.AssetLinkedAssetVehicleTyre.TyrePosition
			}
		}
	}
}
