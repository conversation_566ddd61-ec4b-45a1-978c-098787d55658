package presistence

import (
	"assetfindr/internal/app/integration/constants"
	"assetfindr/internal/app/integration/models"
	"assetfindr/internal/app/integration/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"context"
	"errors"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type integrationRepository struct{}

func NewIntegrationRepository() repository.IntegrationRepository {
	return &integrationRepository{}
}

func (r *integrationRepository) CreateIntegrationAccount(ctx context.Context, dB database.DBI, integrationAccount *models.IntegrationAccount) error {
	return dB.GetTx().Create(integrationAccount).Error
}

func enrichIntegrationAccountQueryWithWhere(query *gorm.DB, where models.IntegrationAccountWhere) {
	if where.ID != "" {
		query.Where("id = ?", where.ID)
	} // ID

	if where.TargetCode != "" {
		query.Where("target_code = ?", where.TargetCode)
	} // TargetCode

	if len(where.TargetCodes) > 0 {
		query.Where("target_code IN ?", where.TargetCodes)
	} // TargetCodes

	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	} // ClientID

	if len(where.ClientIDs) > 0 {
		query.Where("client_id IN ?", where.ClientIDs)
	} // ClientIDs

	if where.ClientIDOrGeneral != "" {
		query.Where("(client_id = ? OR client_id = 'GENERAL')", where.ClientIDOrGeneral)
	} // ClientIDOrGeneral

	if !where.ShowDeleted {
		query.Where("status_code != ?", constants.INTEGRATION_STATUS_CODE_DELETED)
	} // ShowDeleted

	if where.WithOrmDeleted {
		query.Unscoped()
	}
}

func enrichIntegrationAccountQueryWithPreload(query *gorm.DB, preload models.IntegrationAccountPreload) {
	if preload.IntegrationTarget {
		query.Preload("IntegrationTarget")
	} // IntegrationTarget
}

func (r *integrationRepository) GetIntegrationAccount(ctx context.Context, dB database.DBI, cond models.IntegrationAccountCondition) (*models.IntegrationAccount, error) {
	integrationAccountation := models.IntegrationAccount{}
	query := dB.GetOrm().Model(&integrationAccountation)

	enrichIntegrationAccountQueryWithWhere(query, cond.Where)
	enrichIntegrationAccountQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.First(&integrationAccountation).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("integration account")
		}

		return nil, err
	}

	return &integrationAccountation, nil
}

func (r *integrationRepository) GetIntegrationAccountList(ctx context.Context, dB database.DBI, param models.GetIntegrationAccountListParam) (int, []models.IntegrationAccount, error) {
	var totalRecords int64
	integrationAccounts := []models.IntegrationAccount{}
	query := dB.GetTx().Model(&integrationAccounts)
	if param.SearchKeyword != "" {
		query = query.Where("LOWER(name) ILIKE LOWER(?)", "%"+param.SearchKeyword+"%")
	}

	enrichIntegrationAccountQueryWithWhere(query, param.Cond.Where)
	enrichIntegrationAccountQueryWithPreload(query, param.Cond.Preload)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("updated_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&integrationAccounts).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), integrationAccounts, nil
}

func enrichIntegrationTargetQueryWithWhere(query *gorm.DB, where models.IntegrationTargetWhere) {
}

func (r *integrationRepository) GetIntegrationAccountTargetList(ctx context.Context, dB database.DBI, param models.GetIntegrationTargetListParam) (int, []models.IntegrationTarget, error) {
	var totalRecords int64
	integrationTargets := []models.IntegrationTarget{}
	query := dB.GetTx().Model(&integrationTargets)
	enrichIntegrationTargetQueryWithWhere(query, param.Cond.Where)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&integrationTargets).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), integrationTargets, nil
}

func (r *integrationRepository) GetIntegrationAccountTargetType(ctx context.Context, dB database.DBI, cond models.IntegrationTargetTypeCondition) (*models.IntegrationTargetType, error) {
	integrationTargetType := models.IntegrationTargetType{}
	query := dB.GetOrm().Model(&integrationTargetType)

	enrichIntegrationTargetTypeQueryWithWhere(query, cond.Where)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.First(&integrationTargetType).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("integration target type")
		}

		return nil, err
	}

	return &integrationTargetType, nil
}

func enrichIntegrationTargetTypeQueryWithWhere(query *gorm.DB, where models.IntegrationTargetTypeWhere) {
	if where.Code != "" {
		query.Where("code = ?", where.Code)
	}

	if where.TypeCode != "" {
		query.Where("integration_type_code = ?", where.TypeCode)
	}
}

func (r *integrationRepository) GetIntegrationAccountTargetTypeList(ctx context.Context, dB database.DBI, param models.GetIntegrationTargetTypeListParam) (int, []models.IntegrationTargetType, error) {
	var totalRecords int64
	integrationTargetTypes := []models.IntegrationTargetType{}
	query := dB.GetTx().Model(&integrationTargetTypes)
	enrichIntegrationTargetTypeQueryWithWhere(query, param.Cond.Where)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&integrationTargetTypes).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), integrationTargetTypes, nil
}

func (r *integrationRepository) UpdateIntegrationAccount(ctx context.Context, dB database.DBI, id string, integrationAccount *models.IntegrationAccount) error {
	return dB.GetTx().
		Where("id = ?", id).
		Updates(integrationAccount).
		Error
}

func (r *integrationRepository) CreateIntegration(ctx context.Context, dB database.DBI, integration *models.Integration) error {
	return dB.GetTx().Create(integration).Error
}

func (r *integrationRepository) UpsertIntegration(ctx context.Context, dB database.DBI, integration *models.Integration) error {
	tx := dB.GetTx()

	var existing models.Integration
	err := tx.
		Where("internal_reference_id = ? AND client_id = ?", integration.InternalReferenceID, integration.ClientID).
		First(&existing).Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return tx.Create(integration).Error
	} else if err != nil {
		return err
	}

	integration.ID = existing.ID
	return tx.
		Where("id = ?", existing.ID).
		Updates(integration).
		Error
}

func enrichIntegrationQueryWithWhere(query *gorm.DB, where models.IntegrationWhere) {
	if where.ID != "" {
		query.Where("id = ?", where.ID)
	} // ID

	if where.IntegrationTargetTypeCode != "" {
		query.Where("integration_target_type_code = ?", where.IntegrationTargetTypeCode)
	} // IntegrationTargetTypeCode

	if len(where.IntegrationTargetTypeCodes) > 0 {
		query.Where("integration_target_type_code IN ?", where.IntegrationTargetTypeCodes)
	} // IntegrationTargetTypeCodes

	if where.Status != "" {
		query.Where("status_code = ?", where.Status)
	} // Status

	if where.IdentifierJSON != nil {
		for k, v := range where.IdentifierJSON {
			query.Where("identifier_json->>? = ?", k, v)
		}
	} // IntegrationTargetTypeCode

	if where.IntegrationTypeCode != "" {
		query.Joins(
			`JOIN "ins_INTEGRATION_TARGET_TYPE" iitt ON iitt.code  = ins_integrations.integration_target_type_code AND iitt.integration_type_code = ?`,
			where.IntegrationTypeCode)
	} // IntegrationTypeCode

	if len(where.IntegrationTypeCodes) > 0 {
		query.Joins(
			`JOIN "ins_INTEGRATION_TARGET_TYPE" iitt ON iitt.code  = ins_integrations.integration_target_type_code AND iitt.integration_type_code IN ?`,
			where.IntegrationTypeCodes)
	} // IntegrationTypeCodes

	if where.InternalReferenceID != "" {
		query.Where("internal_reference_id = ?", where.InternalReferenceID)
	} // InternalReferenceID

	if len(where.InternalReferenceIDs) > 0 {
		query.Where("internal_reference_id IN ?", where.InternalReferenceIDs)
	} // InternalReferenceIDs

	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	} // ClientID

	if where.WithOrmDeleted {
		query.Unscoped()
	}

	if where.HideOnMonitoring {
		query.Where("hide_on_monitoring != ?", where.HideOnMonitoring)
	}

	if where.IsEnableCommand {
		query.Where("is_enable_command = true")
	} // IsEnableCommand

	if len(where.ICCIDs) > 0 {
		query.Where("iccid IN ?", where.ICCIDs)
	} // ICCIDs

}

func enrichIntegrationQueryWithPreload(query *gorm.DB, preload models.IntegrationPreload) {
	if preload.IntegrationAccount {
		query.Preload("IntegrationAccount")
	} // IntegrationAccount

	if preload.IntegrationAccountTarget {
		query.Preload("IntegrationAccount.IntegrationTarget")
	} // IntegrationAccountTarget

	if preload.IntegrationTargetType {
		query.Preload("IntegrationTargetType")
	} // IntegrationTargetType

	if preload.DataMapping {
		query.Preload("DataMapping")
	} // DataMapping

	if preload.Asset {
		query.Preload("AssetDetail")
	} // DataMapping
}

func (r *integrationRepository) GetIntegration(ctx context.Context, dB database.DBI, cond models.IntegrationCondition) (*models.Integration, error) {
	integrationation := models.Integration{}
	query := dB.GetOrm().Model(&integrationation)

	enrichIntegrationQueryWithWhere(query, cond.Where)
	enrichIntegrationQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.First(&integrationation).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("integration")
		}

		return nil, err
	}

	return &integrationation, nil
}

func (r *integrationRepository) GetIntegrations(ctx context.Context, dB database.DBI, cond models.IntegrationCondition) ([]models.Integration, error) {
	integrationations := []models.Integration{}
	query := dB.GetOrm().Model(&integrationations)

	enrichIntegrationQueryWithWhere(query, cond.Where)
	enrichIntegrationQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.Find(&integrationations).Error
	if err != nil {
		return nil, err
	}

	return integrationations, nil
}

func (r *integrationRepository) GetIntegrationList(ctx context.Context, dB database.DBI, param models.GetIntegrationListParam) (int, []models.Integration, error) {
	var totalRecords int64
	integrations := []models.Integration{}
	query := dB.GetTx().Model(&integrations)
	enrichIntegrationQueryWithWhere(query, param.Cond.Where)
	enrichIntegrationQueryWithPreload(query, param.Cond.Preload)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("updated_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&integrations).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), integrations, nil
}

func (r *integrationRepository) GetIntegrationDistinctRefIDList(ctx context.Context, dB database.DBI, param models.GetIntegrationListParam) (int, []string, error) {
	var totalRecords int64
	integrationInternalRefIDs := []string{}
	query := dB.GetTx().Model(&models.Integration{})
	enrichIntegrationQueryWithWhere(query, param.Cond.Where)
	enrichIntegrationQueryWithPreload(query, param.Cond.Preload)

	query.Distinct("internal_reference_id")
	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Pluck("internal_reference_id", &integrationInternalRefIDs).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), integrationInternalRefIDs, nil
}

func (r *integrationRepository) GetIntegrationTrackingList(ctx context.Context, dB database.DBI, param models.GetIntegrationListParam) (int, []models.Integration, error) {
	var totalRecords int64
	integrations := []models.Integration{}
	query := dB.GetTx().Model(&integrations)

	query.Joins(
		`JOIN "ins_INTEGRATION_TARGET_TYPE" iitt ON iitt.code  = ins_integrations.integration_target_type_code AND iitt.integration_type_code = ?`,
		constants.INTEGRATION_TYPE_CODE_TRACKING)

	if param.SearchKeyword != "" {
		query.Where("LOWER(name) ILIKE LOWER(?)", "%"+param.SearchKeyword+"%")
	}
	enrichIntegrationQueryWithWhere(query, param.Cond.Where)
	enrichIntegrationQueryWithPreload(query, param.Cond.Preload)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("updated_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&integrations).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), integrations, nil
}

func (r *integrationRepository) UpdateIntegration(ctx context.Context, dB database.DBI, id string, integration *models.Integration) error {
	return dB.GetTx().
		Where("id = ?", id).
		Updates(integration).
		Error
}

func (r *integrationRepository) DeleteIntegration(ctx context.Context, dB database.DBI, id string) error {
	return dB.GetTx().
		Delete(&models.Integration{}, "id = ?", id).
		Error
}

func (r *integrationRepository) GetIntegrationVehicleList(ctx context.Context, dB database.DBI, param models.GetIntegrationListParam) (int, []models.Integration, error) {
	var totalRecords int64
	integrations := []models.Integration{}
	query := dB.GetTx().Model(&integrations)
	query.Joins(`INNER JOIN "ins_INTEGRATION_TARGET_TYPE" ON "ins_INTEGRATION_TARGET_TYPE".code = ins_integrations.integration_target_type_code AND "ins_INTEGRATION_TARGET_TYPE".integration_type_code = ?`, param.Cond.Where.IntegrationTypeCode)
	query.Where("client_id = ?", param.Cond.Where.ClientID)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("updated_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&integrations).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), integrations, nil
}

func (r *integrationRepository) UpsertIntegrationSession(ctx context.Context, dB database.DBI, integrationSession *models.IntegrationSession) error {
	return dB.GetTx().
		Clauses(clause.OnConflict{
			Columns: []clause.Column{
				{Name: "integration_account_id"},
			},
			DoUpdates: clause.AssignmentColumns([]string{"updated_at", "data", "updated_by"}),
		}).
		Create(integrationSession).Error
}

func enrichIntegrationSessionQueryWithWhere(query *gorm.DB, where models.IntegrationSessionWhere) {
	if where.ID != "" {
		query.Where("id = ?", where.ID)
	} // ID

	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	} // ClientID

	if where.SessionTypeCode != "" {
		query.Where("session_type_code = ?", where.SessionTypeCode)
	} // SessionTypeCode
}

func enrichIntegrationSessionQueryWithPreload(query *gorm.DB, preload models.IntegrationSessionPreload) {

}

func (r *integrationRepository) GetIntegrationSession(ctx context.Context, dB database.DBI, cond models.IntegrationSessionCondition) (*models.IntegrationSession, error) {
	integrationation := models.IntegrationSession{}
	query := dB.GetOrm().Model(&integrationation)

	enrichIntegrationSessionQueryWithWhere(query, cond.Where)
	enrichIntegrationSessionQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.First(&integrationation).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("integration session")
		}

		return nil, err
	}

	return &integrationation, nil
}

func enrichIntegrationTranslogicCalibrationQueryWithWhere(query *gorm.DB, where models.IntegrationTranslogicCalibrationWhere) {
	if where.ID != "" {
		query.Where("id = ?", where.ID)
	} // ID

	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	} // ClientID
}

func (r *integrationRepository) GetIntegrationTranslogicCalibrationList(ctx context.Context, dB database.DBI, param models.GetIntegrationTranslogicCalibrationListParam) (int, []models.IntegrationTranslogicCalibration, error) {
	var totalRecords int64
	translogicCalibrations := []models.IntegrationTranslogicCalibration{}
	query := dB.GetTx().Model(&translogicCalibrations)
	if param.SearchKeyword != "" {
		query = query.Where("LOWER(device_id) LIKE LOWER(?)", "%"+param.SearchKeyword+"%")
	}

	enrichIntegrationTranslogicCalibrationQueryWithWhere(query, param.Cond.Where)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("updated_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&translogicCalibrations).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), translogicCalibrations, nil
}

func (r *integrationRepository) CreateTranslogicCalibration(ctx context.Context, dB database.DBI, translogicCalibration *models.IntegrationTranslogicCalibration) error {
	return dB.GetTx().Create(translogicCalibration).Error
}

func (r *integrationRepository) UpsertMonitoringDisplayConfig(ctx context.Context, dB database.DBI, monitoringDisCon *models.MonitoringDisplayConfig) error {
	return dB.GetTx().
		Clauses(clause.OnConflict{
			Columns: []clause.Column{
				{Name: "client_id"},
			},
			DoUpdates: clause.AssignmentColumns([]string{"updated_at", "data_mapping_keys", "updated_by"}),
		}).
		Create(monitoringDisCon).Error
}

func (r *integrationRepository) GetMonitoringDisplayConfig(ctx context.Context, dB database.DBI, clientID string) (*models.MonitoringDisplayConfig, error) {
	result := models.MonitoringDisplayConfig{}
	err := dB.GetTx().Model(&models.MonitoringDisplayConfig{}).
		Where("client_id = ?", clientID).
		First(&result).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("monitoring display config")
		}
		return nil, err
	}

	return &result, nil
}

func (r *integrationRepository) CreateIntegrationDataMappingTemplate(ctx context.Context, dB database.DBI, integrationDataMappingTemplate *models.IntegrationDataMappingTemplate) error {
	return dB.GetTx().Create(integrationDataMappingTemplate).Error
}

func enrichIntegrationDataMappingTemplateQueryWithWhere(query *gorm.DB, where models.IntegrationDataMappingTemplateWhere) {
	if where.Code != "" {
		query.Where("code = ?", where.Code)
	} // ID
}

func (r *integrationRepository) GetIntegrationDataMappingTemplate(ctx context.Context, dB database.DBI, cond models.IntegrationDataMappingTemplateCondition) (*models.IntegrationDataMappingTemplate, error) {
	integrationDataMappingTemplate := models.IntegrationDataMappingTemplate{}
	query := dB.GetOrm().Model(&integrationDataMappingTemplate)

	enrichIntegrationDataMappingTemplateQueryWithWhere(query, cond.Where)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.First(&integrationDataMappingTemplate).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("integration data mapping template")
		}

		return nil, err
	}

	return &integrationDataMappingTemplate, nil
}

func (r *integrationRepository) GetIntegrationDataMappingTemplateList(ctx context.Context, dB database.DBI, param models.GetIntegrationDataMappingTemplateListParam) (int, []models.IntegrationDataMappingTemplate, error) {
	var totalRecords int64
	integrationDataMappingTemplates := []models.IntegrationDataMappingTemplate{}
	query := dB.GetTx().Model(&integrationDataMappingTemplates)
	if param.SearchKeyword != "" {
		query = query.Where("LOWER(label) ILIKE LOWER(?)", "%"+param.SearchKeyword+"%")
	}

	enrichIntegrationDataMappingTemplateQueryWithWhere(query, param.Cond.Where)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("updated_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&integrationDataMappingTemplates).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), integrationDataMappingTemplates, nil
}

func (r *integrationRepository) UpdateIntegrationDataMappingTemplate(ctx context.Context, dB database.DBI, id string, integrationDataMappingTemplate *models.IntegrationDataMappingTemplate) error {
	return dB.GetTx().
		Where("code = ?", id).
		Updates(integrationDataMappingTemplate).
		Error
}

func (r *integrationRepository) DeActivateIntegrationByRefID(ctx context.Context, dB database.DBI, internalRefID string) error {
	err := dB.GetTx().
		Where("internal_reference_id = ?", internalRefID).
		Updates(&models.Integration{StatusCode: constants.ALERT_CONFIG_STATUS_CODE_INACTIVE}).
		Error

	if err != nil {
		return err
	}

	return nil
}

func enrichIntegrationCommandsQueryWithPreload(query *gorm.DB, preload models.IntegrationCommandsPreload) {
	if preload.IntegrationCommandStatus {
		query.Preload("IntegrationCommandStatus")
	} // IntegrationTarget

	if preload.IntegrationCommandType {
		query.Preload("IntegrationCommandType")
	} // IntegrationCommandType

}

func (r *integrationRepository) GetIntegrationCommandsList(ctx context.Context, dB database.DBI, param models.IntegrationCommandsListParam) (int, []models.IntegrationCommand, error) {
	var totalRecords int64
	integrationsCommands := []models.IntegrationCommand{}
	query := dB.GetTx().Model(&integrationsCommands)

	enrichIntegrationCommandsQueryWithPreload(query, param.Cond.Preload)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("updated_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&integrationsCommands).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), integrationsCommands, nil
}

func (r *integrationRepository) CreateIntegrationCommand(ctx context.Context, dB database.DBI, integrationCommand *models.IntegrationCommand) error {
	return dB.GetTx().Create(integrationCommand).Error
}

func enrichManualCanBusMappingQueryWithWhere(query *gorm.DB, where models.ManualCanBusMappingWhere) {
	if where.CanIdCode != "" {
		query.Where("can_id_code = ?", where.CanIdCode)
	}
}

func (r *integrationRepository) GetManualCanBusMappings(ctx context.Context, dB database.DBI, cond models.ManualCanBusMappingCondition) ([]models.ManualCanBusMapping, error) {
	integrationations := []models.ManualCanBusMapping{}
	query := dB.GetOrm().Model(&integrationations)

	enrichManualCanBusMappingQueryWithWhere(query, cond.Where)

	err := query.Find(&integrationations).Error
	if err != nil {
		return nil, err
	}

	return integrationations, nil
}

func enrichIntegrationDeviceQueryWithWhere(query *gorm.DB, where models.IntegrationDeviceWhere) {
	if where.ClientID != "" {
		query.Where("ins_integration_devices.client_id = ?", where.ClientID)
	} // Client ID

	if where.ID != "" {
		query.Where("ins_integration_devices.id = ?", where.ID)
	} // ID

	if where.IntegrationID != "" {
		query.Where("ins_integration_devices.integration_id = ?", where.IntegrationID)
	} // IntegrationID

	if len(where.IntegrationIDs) > 0 {
		query.Where("ins_integration_devices.integration_id IN ?", where.IntegrationIDs)
	} // IntegrationIDs

	if where.IntegrationTypeCode != "" {
		query.Where("ins_integration_devices.integration_type_code = ?", where.IntegrationTypeCode)
	} // IntegrationTypeCode

	if len(where.IntegrationTypeCodes) > 0 {
		query.Where("ins_integration_devices.integration_type_code IN ?", where.IntegrationTypeCodes)
	} // IntegrationTypeCodes

	if where.ReferenceCode != "" {
		query.Where("ins_integration_devices.reference_code = ?", where.ReferenceCode)
	} // ReferenceCode

	if len(where.ReferenceCodes) > 0 {
		query.Where("ins_integration_devices.reference_code IN ?", where.ReferenceCodes)
	} // ReferenceCodes

	if where.AttachedByUserID != "" {
		query.Where("ins_integration_devices.attached_by_user_id = ?", where.AttachedByUserID)
	} // AttachedByUserID

	if len(where.AttachedByUserIDs) > 0 {
		query.Where("ins_integration_devices.attached_by_user_id IN ?", where.AttachedByUserIDs)
	} // AttachedByUserIDs

	if !where.AttachedAtStart.IsZero() {
		query.Where("ins_integration_devices.attached_at >= ?", where.AttachedAtStart)
	} // AttachedAtStart

	if !where.AttachedAtEnd.IsZero() {
		query.Where("ins_integration_devices.attached_at <= ?", where.AttachedAtEnd)
	} // AttachedAtEnd

	if where.AssetID != "" {
		query.Where("ams_assets.id = ? OR parent_assets.id = ?", where.AssetID, where.AssetID)
	} // AssetID

	if len(where.AssetIDs) > 0 {
		query.Where("ams_assets.id IN ? OR parent_assets.id IN ?", where.AssetIDs, where.AssetIDs)
	} // AssetIDs
}

func enrichIntegrationDeviceQueryWithPreload(query *gorm.DB, preload models.IntegrationDevicePreload) {
	if preload.ActiveIntegration {
		query.Preload("Integration", func(db *gorm.DB) *gorm.DB {
			return db.Where("ins_integrations.status_code = ?", constants.INTEGRATION_STATUS_CODE_ACTIVE)
		})
	} // Integration

	if preload.IntegrationAsset {
		query.Preload("Integration.AssetDetail")
	} // IntegrationAsset

	if preload.IntegrationAssetTyreWithLinkedParent {
		query.Preload(
			"Integration.AssetDetail.AssetTyre.AssetLinked", "unlinked_datetime IS NULL",
		).
			Preload("Integration.AssetDetail.AssetTyre.AssetLinked.AssetParent").
			Preload("Integration.AssetDetail.AssetTyre.Tyre").
			Preload("Integration.AssetDetail.AssetTyre.AssetLinked.AssetLinkedAssetVehicleTyre")
	} // IntegrationAssetTyreWithLinkedParent
}

func (r *integrationRepository) GetIntegrationDeviceList(ctx context.Context, dB database.DBI, param models.GetIntegrationDeviceListParam) (int, []models.IntegrationDevice, error) {
	var totalRecords int64
	integrationDevices := []models.IntegrationDevice{}
	query := dB.GetTx().Model(&integrationDevices)

	query.Joins("LEFT JOIN ins_integrations ON ins_integration_devices.integration_id = ins_integrations.id").
		Joins("LEFT JOIN ams_assets ON ams_assets.id = ins_integrations.internal_reference_id").
		Joins("LEFT JOIN ams_linked_assets ON ams_linked_assets.child_asset_id = ams_assets.id AND ams_linked_assets.unlinked_datetime IS NULL").
		Joins("LEFT JOIN ams_assets parent_assets ON ams_linked_assets.parent_asset_id = parent_assets.id").
		Joins("LEFT JOIN uis_clients ON uis_clients.id = ins_integration_devices.client_id")

	enrichIntegrationDeviceQueryWithWhere(query, param.Cond.Where)
	enrichIntegrationDeviceQueryWithPreload(query, param.Cond.Preload)

	if param.Cond.Where.SerialOrReferenceNumber != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(parent_assets.serial_number) = ?", param.Cond.Where.SerialOrReferenceNumber).
				Or("LOWER(parent_assets.reference_number) = ?", param.Cond.Where.SerialOrReferenceNumber),
		)
	}

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(ins_integrations.client_id) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ins_integration_devices.reference_code) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(parent_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(parent_assets.reference_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.reference_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(uis_clients.name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(uis_clients.client_alias) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("updated_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&integrationDevices).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), integrationDevices, nil
}

func (r *integrationRepository) GetIntegrationWithActiveIntegrationDevice(ctx context.Context, dB database.DBI, assetTyreID string) (*models.Integration, error) {
	var integration models.Integration

	err := dB.GetTx().Table("ins_integrations").
		Joins("JOIN ins_integration_devices ON ins_integrations.id = ins_integration_devices.integration_id").
		Where("ins_integrations.internal_reference_id = ?", assetTyreID).
		First(&integration).Error

	if err != nil {
		return nil, err
	}

	return &integration, nil
}

func (r *integrationRepository) CreateIntegrationDevice(ctx context.Context, dB database.DBI, integrationDevice *models.IntegrationDevice) error {
	return dB.GetTx().Create(integrationDevice).Error
}

func (r *integrationRepository) UpdateIntegrationDevice(ctx context.Context, dB database.DBI, id string, integrationDevice *models.IntegrationDevice) error {
	updates := map[string]interface{}{
		"integration_id":      integrationDevice.IntegrationID,
		"attached_by_user_id": integrationDevice.AttachedByUserID,
		"attached_at":         integrationDevice.AttachedAt,
		"reference_code":      integrationDevice.ReferenceCode,
		"client_id":           integrationDevice.ClientID,
	}

	return dB.GetTx().
		Where("id = ?", id).
		Model(&models.IntegrationDevice{}).
		Updates(updates).
		Error
}

func enrichIntegrationDeviceHistoryQueryWithWhere(query *gorm.DB, where models.IntegrationDeviceHistoryWhere) {
	if where.ClientID != "" {
		query.Where("ins_integration_device_histories.client_id = ?", where.ClientID)
	} // Client ID

	if where.ID != "" {
		query.Where("ins_integration_device_histories.id = ?", where.ID)
	} // ID

	if where.IntegrationID != "" {
		query.Where("ins_integration_device_histories.integration_id = ?", where.IntegrationID)
	} // IntegrationID

	if len(where.IntegrationIDs) > 0 {
		query.Where("ins_integration_device_histories.integration_id IN ?", where.IntegrationIDs)
	} // IntegrationIDs

	if where.IntegrationDeviceID != "" {
		query.Where("ins_integration_device_histories.integration_device_id = ?", where.IntegrationDeviceID)
	} // IntegrationDeviceID

	if len(where.IntegrationDeviceIDs) > 0 {
		query.Where("ins_integration_device_histories.integration_device_id IN ?", where.IntegrationDeviceIDs)
	} // IntegrationIDs

	if where.AttachedByUserID != "" {
		query.Where("ins_integration_device_histories.attached_by_user_id = ?", where.AttachedByUserID)
	} // AttachedByUserID

	if len(where.AttachedByUserIDs) > 0 {
		query.Where("ins_integration_device_histories.attached_by_user_id IN ?", where.AttachedByUserIDs)
	} // AttachedByUserIDs

	if where.DetachedByUserID != "" {
		query.Where("ins_integration_device_histories.detached_by_user_id = ?", where.DetachedByUserID)
	} // DetachedByUserID

	if len(where.DetachedByUserIDs) > 0 {
		query.Where("ins_integration_device_histories.detached_by_user_id IN ?", where.DetachedByUserIDs)
	} // DetachedByUserIDs

	if !where.AttachedAtStart.IsZero() {
		query.Where("ins_integration_device_histories.attached_at >= ?", where.AttachedAtStart)
	} // AttachedAtStart

	if !where.AttachedAtEnd.IsZero() {
		query.Where("ins_integration_device_histories.attached_at <= ?", where.AttachedAtEnd)
	} // AttachedAtEnd

	if !where.DetachedAtStart.IsZero() {
		query.Where("ins_integration_device_histories.detached_at >= ?", where.DetachedAtStart)
	} // DetachedAtStart

	if !where.DetachedAtEnd.IsZero() {
		query.Where("ins_integration_device_histories.detached_at <= ?", where.DetachedAtEnd)
	} // DetachedAtEnd
}

func enrichIntegrationDeviceHistoryQueryWithPreload(query *gorm.DB, preload models.IntegrationDeviceHistoryPreload) {
	if preload.IntegrationDevice {
		query.Preload("IntegrationDevice")
	} // IntegrationDevice

	if preload.IntegrationDeviceIntegration {
		query.Preload("IntegrationDevice.Integration")
	} // Integration

	if preload.IntegrationDeviceIntegrationAsset {
		query.Preload("IntegrationDevice.Integration.AssetDetail")
	} // IntegrationAsset

	if preload.IntegrationDeviceIntegrationAssetTyreWithLinkedParent {
		query.Preload(
			"IntegrationDevice.Integration.AssetDetail.AssetTyre.AssetLinked", "unlinked_datetime IS NULL",
		).
			Preload("IntegrationDevice.Integration.AssetDetail.AssetTyre.AssetLinked.AssetParent").
			Preload("IntegrationDevice.Integration.AssetDetail.AssetTyre.Tyre").
			Preload("IntegrationDevice.Integration.AssetDetail.AssetTyre.AssetLinked.AssetLinkedAssetVehicleTyre")
	} // IntegrationAssetTyreWithLinkedParent

	if preload.Integration {
		query.Preload("Integration")
	} // Integration

	if preload.IntegrationAsset {
		query.Preload("Integration.AssetDetail")
	} // IntegrationAsset

	if preload.IntegrationAssetTyreWithLinkedParent {
		query.Preload(
			"Integration.AssetDetail.AssetTyre.AssetLinked", "unlinked_datetime IS NULL",
		).
			Preload("Integration.AssetDetail.AssetTyre.AssetLinked.AssetParent").
			Preload("Integration.AssetDetail.AssetTyre.Tyre").
			Preload("Integration.AssetDetail.AssetTyre.AssetLinked.AssetLinkedAssetVehicleTyre")
	} // IntegrationAssetTyreWithLinkedParent
}

func (r *integrationRepository) GetIntegrationDeviceHistoryList(ctx context.Context, dB database.DBI, param models.GetIntegrationDeviceHistoryListParam) (int, []models.IntegrationDeviceHistory, error) {
	var totalRecords int64
	histories := []models.IntegrationDeviceHistory{}
	query := dB.GetTx().Model(&histories)

	query.Joins("JOIN ins_integration_devices ON ins_integration_devices.id = ins_integration_device_histories.integration_device_id").
		Joins("JOIN ins_integrations ON ins_integration_device_histories.integration_id = ins_integrations.id").
		Joins("LEFT JOIN ams_assets ON ams_assets.id = ins_integrations.internal_reference_id").
		Joins("LEFT JOIN ams_linked_assets ON ams_linked_assets.child_asset_id = ams_assets.id").
		Joins("LEFT JOIN ams_assets parent_assets ON ams_linked_assets.parent_asset_id = parent_assets.id").
		Where("ams_linked_assets.unlinked_datetime IS NULL")

	enrichIntegrationDeviceHistoryQueryWithWhere(query, param.Cond.Where)
	enrichIntegrationDeviceHistoryQueryWithPreload(query, param.Cond.Preload)

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(ins_integration_devices.reference_code) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(parent_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(parent_assets.reference_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("updated_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&histories).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), histories, nil
}

func (r *integrationRepository) GetLatestIntegrationDeviceHistory(ctx context.Context, dB database.DBI, cond models.IntegrationDeviceHistoryCondition) (*models.IntegrationDeviceHistory, error) {
	history := models.IntegrationDeviceHistory{}
	query := dB.GetTx().Model(&history)

	enrichIntegrationDeviceHistoryQueryWithWhere(query, cond.Where)

	query.Order("updated_at DESC")
	err := query.First(&history).Error
	if err != nil {
		return nil, err
	}

	return &history, nil
}

func (r *integrationRepository) CreateIntegrationDeviceHistory(ctx context.Context, dB database.DBI, integrationDeviceHistory *models.IntegrationDeviceHistory) error {
	return dB.GetTx().Create(integrationDeviceHistory).Error
}

func (r *integrationRepository) UpdateIntegrationDeviceHistory(ctx context.Context, dB database.DBI, id string, integrationDeviceHistory *models.IntegrationDeviceHistory) error {
	return dB.GetTx().
		Where("id = ?", id).
		Updates(integrationDeviceHistory).
		Error
}

func (r *integrationRepository) GetIntegrationDeviceDetail(ctx context.Context, dB database.DBI, id string) (models.IntegrationDevice, error) {
	integrationDevice := models.IntegrationDevice{}
	query := dB.GetTx().Model(&integrationDevice).Preload("Integration").Where("id = ?", id)

	err := query.Debug().First(&integrationDevice).Error
	if err != nil {
		return integrationDevice, err
	}

	return integrationDevice, nil
}

func (r *integrationRepository) GetIntegrationDevice(ctx context.Context, dB database.DBI, cond models.IntegrationDeviceCondition) (*models.IntegrationDevice, error) {
	integrationDevice := models.IntegrationDevice{}
	query := dB.GetTx().Model(&integrationDevice)

	enrichIntegrationDeviceQueryWithWhere(query, cond.Where)
	enrichIntegrationDeviceQueryWithPreload(query, cond.Preload)

	err := query.First(&integrationDevice).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("integration device")
		}

		return nil, err
	}

	return &integrationDevice, nil
}
