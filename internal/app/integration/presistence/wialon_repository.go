package presistence

import (
	"assetfindr/internal/app/integration/constants"
	"assetfindr/internal/app/integration/models"
	"assetfindr/internal/app/integration/repository"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"math"
	"math/rand"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/patrickmn/go-cache"
)

type wialonRepository struct{}

func NewWialonRepository() repository.WialonRepository {
	return &wialonRepository{}
}

// In-memory cache, default cleanup every 10 minutes
var tokenCache = cache.New(25*24*time.Hour, 10*time.Minute)

const (
	maxRetries          = 3
	baseBackoff         = 200 * time.Millisecond // backoff for transient/relogin retries
	userAgent           = "assetfindr-wialon-client/1.0"
	cacheKeyWialonToken = "wialon_token"
)

// minimal shape Wialon uses in many responses
type wialonResp struct {
	Error  int    `json:"error"`
	Reason string `json:"reason"`
}

// consider these error codes as "session invalid → relogin needed"
func shouldRelogin(code int) bool {
	return code == 1 || code == 8
}

func httpClient() *http.Client {
	return &http.Client{Timeout: 15 * time.Second}
}

func GetWialonToken(ctx context.Context) (string, error) {
	if token, found := tokenCache.Get(cacheKeyWialonToken); found {
		return token.(string), nil
	}

	// Build login URL
	params := url.Values{}
	params.Set("svc", "token/login")
	params.Set("params", fmt.Sprintf(`{"token":"%s"}`, constants.WIALON_TOKEN))

	loginURL := fmt.Sprintf("%s?%s", constants.WIALON_API_BASE_URL, params.Encode())

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, loginURL, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create login request: %w", err)
	}
	req.Header.Set("User-Agent", userAgent)

	resp, err := httpClient().Do(req)
	if err != nil {
		return "", fmt.Errorf("login request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		b, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("login failed with status: %d body: %s", resp.StatusCode, string(b))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read login response: %w", err)
	}

	// Correct struct for actual response
	var result struct {
		EID   string `json:"eid"`
		Token string `json:"token"`
	}
	if err := json.Unmarshal(body, &result); err != nil {
		return "", fmt.Errorf("failed to parse login response: %w", err)
	}
	if result.EID == "" {
		// Try to surface possible error payloads
		var e wialonResp
		if json.Unmarshal(body, &e) == nil && e.Error != 0 {
			return "", fmt.Errorf("login returned error: %d (%s)", e.Error, e.Reason)
		}
		return "", errors.New("login did not return session EID")
	}

	// Cache the session id; Wialon doesn't return TTL here, use fixed expiration
	tokenCache.Set(cacheKeyWialonToken, result.EID, 24*time.Hour)

	return result.EID, nil
}

// Best-effort logout; ignore errors
func logoutWialonSession(ctx context.Context, sid string) {
	if sid == "" {
		return
	}
	params := url.Values{}
	params.Set("svc", "core/logout")
	params.Set("sid", sid)
	urlStr := fmt.Sprintf("%s?%s", constants.WIALON_API_BASE_URL, params.Encode())

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, urlStr, nil)
	if err != nil {
		return
	}
	req.Header.Set("User-Agent", userAgent)
	resp, err := httpClient().Do(req)
	if err != nil {
		return
	}
	io.Copy(io.Discard, resp.Body)
	resp.Body.Close()
}

func invalidateSession(ctx context.Context, sid string) {
	// try to logout first (best effort), then drop cache
	log.Println("[WIALON] Invalidating session and clearing cache")
	logoutWialonSession(ctx, sid)
	tokenCache.Delete(cacheKeyWialonToken)
}

func parsePossibleError(body []byte) (wialonResp, bool) {
	var r wialonResp
	if err := json.Unmarshal(body, &r); err != nil {
		return r, false
	}
	// Some endpoints may return {error:0} on success; treat >=1 as meaningful error
	if r.Error != 0 {
		return r, true
	}
	return r, false
}

func backoffSleep(attempt int) {
	// simple exponential backoff with jitter
	d := time.Duration(float64(baseBackoff) * math.Pow(2, float64(attempt)))
	jitter := time.Duration(rand.Int63n(int64(baseBackoff / 2)))
	time.Sleep(d + jitter)
}

// -----------------------------
// Public helpers (same names)
// -----------------------------

func WialonAPIGet(ctx context.Context, svc string, rawParams string) ([]byte, error) {
	client := httpClient()
	var lastSID string

	for attempt := 0; attempt < maxRetries; attempt++ {
		token, err := GetWialonToken(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to get wialon token: %w", err)
		}
		lastSID = token

		// Build query
		params := url.Values{}
		params.Set("svc", svc)
		if rawParams != "" {
			params.Set("params", rawParams)
		}
		params.Set("sid", token)

		apiURL := fmt.Sprintf("%s?%s", constants.WIALON_API_BASE_URL, params.Encode())
		log.Printf("[WIALON] GET %s (attempt %d)", apiURL, attempt+1)

		req, err := http.NewRequestWithContext(ctx, http.MethodGet, apiURL, nil)
		if err != nil {
			return nil, err
		}
		req.Header.Set("User-Agent", userAgent)

		res, err := client.Do(req)
		if err != nil {
			// transient network—retry with backoff
			log.Printf("[WIALON][NET-ERROR] %v (attempt %d)", err, attempt+1)
			if attempt < maxRetries-1 {
				backoffSleep(attempt)
				continue
			}
			return nil, err
		}
		defer res.Body.Close()

		body, err := io.ReadAll(res.Body)
		if err != nil {
			if attempt < maxRetries-1 {
				backoffSleep(attempt)
				continue
			}
			return nil, err
		}

		if res.StatusCode != http.StatusOK {
			// Non-200; if the JSON says error=1/8, relogin; else fail fast
			if respErr, ok := parsePossibleError(body); ok && shouldRelogin(respErr.Error) {
				log.Printf("[WIALON][AUTH] HTTP %d with error=%d; relogin (attempt %d)", res.StatusCode, respErr.Error, attempt+1)
				invalidateSession(ctx, lastSID)
				if attempt < maxRetries-1 {
					backoffSleep(attempt)
					continue
				}
			}
			return nil, fmt.Errorf("unexpected status code: %d body: %s", res.StatusCode, string(body))
		}

		// 200 OK: check known error envelope
		if respErr, ok := parsePossibleError(body); ok {
			if shouldRelogin(respErr.Error) {
				log.Printf("[WIALON] Invalid/expired session detected (error=%d) — refreshing", respErr.Error)
				invalidateSession(ctx, lastSID)
				if attempt < maxRetries-1 {
					backoffSleep(attempt)
					continue
				}
				return nil, fmt.Errorf("relogin exhausted after error=%d (%s)", respErr.Error, respErr.Reason)
			}
			// Other errors → return as is
			return nil, fmt.Errorf("wialon returned error=%d (%s)", respErr.Error, respErr.Reason)
		}

		return body, nil
	}

	return nil, fmt.Errorf("max retries exceeded")
}

func WialonAPIPost(ctx context.Context, svc string, params map[string]interface{}) ([]byte, error) {
	client := httpClient()
	var lastSID string

	for attempt := 0; attempt < maxRetries; attempt++ {
		token, err := GetWialonToken(ctx)
		if err != nil {
			log.Printf("[WIALON][ERROR] Attempt %d: failed to get token: %v", attempt+1, err)
			return nil, fmt.Errorf("failed to get wialon token: %w", err)
		}
		lastSID = token

		paramsJSON, err := json.Marshal(params)
		if err != nil {
			log.Printf("[WIALON][ERROR] Attempt %d: failed to marshal params: %v", attempt+1, err)
			return nil, fmt.Errorf("failed to marshal params: %w", err)
		}

		formData := url.Values{}
		formData.Set("params", string(paramsJSON))
		formData.Set("sid", token)

		postURL := fmt.Sprintf("%s?svc=%s", constants.WIALON_API_BASE_URL, svc)

		log.Printf("[WIALON] Generated curl: curl -X POST '%s' -H 'Content-Type: application/x-www-form-urlencoded' -d '%s'",
			postURL, formData.Encode())
		log.Printf("[WIALON] POST %s (attempt %d)", postURL, attempt+1)

		req, err := http.NewRequestWithContext(ctx, http.MethodPost, postURL, strings.NewReader(formData.Encode()))
		if err != nil {
			log.Printf("[WIALON][ERROR] Attempt %d: failed to create request: %v", attempt+1, err)
			return nil, err
		}
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
		req.Header.Set("User-Agent", userAgent)

		res, err := client.Do(req)
		if err != nil {
			log.Printf("[WIALON][ERROR] Attempt %d: HTTP request failed: %v", attempt+1, err)
			if attempt < maxRetries-1 {
				backoffSleep(attempt)
				continue
			}
			return nil, err
		}
		defer res.Body.Close()

		body, err := io.ReadAll(res.Body)
		if err != nil {
			log.Printf("[WIALON][ERROR] Attempt %d: failed to read response body: %v", attempt+1, err)
			if attempt < maxRetries-1 {
				backoffSleep(attempt)
				continue
			}
			return nil, err
		}

		if res.StatusCode != http.StatusOK {
			// Non-200; inspect JSON for error=1/8 to relogin
			if respErr, ok := parsePossibleError(body); ok && shouldRelogin(respErr.Error) {
				log.Printf("[WIALON][AUTH] HTTP %d with error=%d; relogin (attempt %d)", res.StatusCode, respErr.Error, attempt+1)
				invalidateSession(ctx, lastSID)
				if attempt < maxRetries-1 {
					backoffSleep(attempt)
					continue
				}
			}
			log.Printf("[WIALON][FAIL] Service: %s | Attempt: %d | HTTP %d | Body: %s",
				svc, attempt+1, res.StatusCode, string(body))
			return nil, fmt.Errorf("unexpected status code: %d body: %s", res.StatusCode, string(body))
		}

		// 200 OK: inspect for Wialon error codes
		if respErr, ok := parsePossibleError(body); ok {
			if shouldRelogin(respErr.Error) {
				log.Printf("[WIALON][WARN] Attempt %d: error=%d (session invalid), refreshing", attempt+1, respErr.Error)
				invalidateSession(ctx, lastSID)
				if attempt < maxRetries-1 {
					backoffSleep(attempt)
					continue
				}
				return nil, fmt.Errorf("relogin exhausted after error=%d (%s)", respErr.Error, respErr.Reason)
			}
			log.Printf("[WIALON][FAIL] Service: %s | Attempt: %d | error=%d | reason=%s | body: %s",
				svc, attempt+1, respErr.Error, respErr.Reason, string(body))
			return nil, fmt.Errorf("wialon returned error=%d (%s)", respErr.Error, respErr.Reason)
		}

		log.Printf("[WIALON][SUCCESS] Service: %s | Attempt: %d | Response: %s", svc, attempt+1, string(body))
		return body, nil
	}

	return nil, fmt.Errorf("[WIALON][ERROR] Service: %s | Max retries (%d) exceeded", svc, maxRetries)
}

func (r *wialonRepository) GetUnitUserAccessList(ctx context.Context, wialonUnitID string) ([]models.WialonGeneral, error) {
	unitID, err := strconv.Atoi(wialonUnitID)
	if err != nil {
		return []models.WialonGeneral{}, fmt.Errorf("invalid unit ID: %w", err)
	}

	// Manually build svc with query-encoded JSON
	svc := "core/check_accessors"
	paramsJSON := fmt.Sprintf(`{"items":[%d],"flags":1}`, unitID)

	body, err := WialonAPIGet(ctx, svc, paramsJSON)
	if err != nil {
		return []models.WialonGeneral{}, fmt.Errorf("WialonAPIGet failed: %w", err)
	}

	// Parse response
	var raw map[string]map[string]struct {
		ACL  int `json:"acl"`
		DACL int `json:"dacl"`
	}
	if err := json.Unmarshal(body, &raw); err != nil {
		return []models.WialonGeneral{}, fmt.Errorf("failed to parse JSON: %w", err)
	}

	var result []models.WialonGeneral

	unitKey := strconv.Itoa(unitID)
	accessorMap, ok := raw[unitKey]
	if !ok {
		return result, nil
	}

	for userID, access := range accessorMap {
		result = append(result, models.WialonGeneral{
			UserID: userID,
			ACL:    access.ACL,
			DACL:   access.DACL,
		})
	}

	return result, nil
}

func (r *wialonRepository) GetUnitList(ctx context.Context, userCreatorName string, unitNameSearchKeyword string) ([]models.WialonGeneral, error) {
	// Build propValueMask from the inputs (supports wildcards)
	propValueMask := fmt.Sprintf("%s*,%s*", userCreatorName, unitNameSearchKeyword)

	params := map[string]interface{}{
		"spec": map[string]interface{}{
			"itemsType":     "avl_unit",
			"propName":      "rel_user_creator_name,sys_name",
			"propValueMask": propValueMask,
			"sortType":      "sys_name",
			"propType":      "property,property",
			"or_logic":      0,
		},
		"force": 1,
		"flags": 1,
		"from":  0,
		"to":    0,
	}

	body, err := WialonAPIPost(ctx, "core/search_items", params)
	if err != nil {
		return []models.WialonGeneral{}, fmt.Errorf("WialonAPIPost failed: %w", err)
	}

	// Parse only needed fields
	var searchResp struct {
		Items []struct {
			ID int64  `json:"id"`
			NM string `json:"nm"`
		} `json:"items"`
	}

	if err := json.Unmarshal(body, &searchResp); err != nil {
		return []models.WialonGeneral{}, fmt.Errorf("failed to parse search_items response: %w", err)
	}

	// Map nm and id into Accessors
	var result []models.WialonGeneral
	for _, item := range searchResp.Items {
		result = append(result, models.WialonGeneral{
			UnitID: fmt.Sprintf("%d", item.ID), // using UserID field to store the unit ID
			Name:   item.NM,                    // you'll need to add 'Name' to models.Accessor if not already present
		})
	}

	return result, nil
}

func (r *wialonRepository) GiveUserAccessToUnit(ctx context.Context, wialonUserID, wialonUnitID string, accessMask int64) error {
	userID, err := strconv.Atoi(wialonUserID)
	if err != nil {
		return fmt.Errorf("invalid unit ID: %w", err)
	}

	unitID, err := strconv.Atoi(wialonUnitID)
	if err != nil {
		return fmt.Errorf("invalid unit ID: %w", err)
	}

	params := map[string]interface{}{
		"userId":     userID,     // The user who will receive access
		"itemId":     unitID,     // The unit to give access to
		"accessMask": accessMask, // Permissions as a bitmask
	}

	body, err := WialonAPIPost(ctx, "user/update_item_access", params)
	if err != nil {
		return fmt.Errorf("failed to update item access: %w", err)
	}

	// Optional: parse response for debugging or confirmation
	var response struct {
		Error  int    `json:"error,omitempty"`
		Reason string `json:"reason,omitempty"`
	}
	if err := json.Unmarshal(body, &response); err == nil && response.Error != 0 {
		return fmt.Errorf("access update failed: %s", response.Reason)
	}

	return nil
}

func generateRandomPassword(username string, length int) string {
	if length < 8 {
		length = 8 // enforce minimum length
	}

	lowerChars := "abcdefghijklmnopqrstuvwxyz"
	upperChars := "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	numberChars := "0123456789"
	specialChars := "!@#$%^&*()-_=+[]{}<>?/"

	allChars := lowerChars + upperChars + numberChars + specialChars
	seed := rand.New(rand.NewSource(time.Now().UnixNano()))

	// Ensure at least one of each required type
	password := []byte{
		lowerChars[seed.Intn(len(lowerChars))],
		upperChars[seed.Intn(len(upperChars))],
		numberChars[seed.Intn(len(numberChars))],
		specialChars[seed.Intn(len(specialChars))],
	}

	// Fill the rest with random characters from all
	for len(password) < length {
		password = append(password, allChars[seed.Intn(len(allChars))])
	}

	// Shuffle to avoid predictable order
	rand.Shuffle(len(password), func(i, j int) {
		password[i], password[j] = password[j], password[i]
	})

	// Ensure password is different from username
	if strings.EqualFold(string(password), username) {
		return generateRandomPassword(username, length)
	}

	return string(password)
}

func (r *wialonRepository) CreateWialonAccount(ctx context.Context, email string, clientAlias string) (models.WialonGeneral, error) {
	if strings.TrimSpace(email) == "" {
		return models.WialonGeneral{}, errors.New("email cannot be empty")
	}
	if strings.TrimSpace(clientAlias) == "" {
		return models.WialonGeneral{}, errors.New("clientAlias cannot be empty")
	}

	// Step 1: Get Client Wialon Main User Id
	params := map[string]interface{}{
		"spec": map[string]interface{}{
			"itemsType":     "user",
			"propName":      "sys_name",
			"propValueMask": clientAlias, // e.g., "batara"
			"sortType":      "sys_name",
		},
		"force": 1,
		"flags": 1,
		"from":  0,
		"to":    0,
	}

	body, err := WialonAPIPost(ctx, "core/search_items", params)
	if err != nil {
		return models.WialonGeneral{}, fmt.Errorf("search main client user failed: %w", err)
	}

	// Parse only needed fields
	var searchResp struct {
		Items []struct {
			ID int64  `json:"id"`
			NM string `json:"nm"`
		} `json:"items"`
	}
	if err := json.Unmarshal(body, &searchResp); err != nil {
		return models.WialonGeneral{}, fmt.Errorf("failed to parse search_items response: %w", err)
	}

	if len(searchResp.Items) == 0 {
		return models.WialonGeneral{}, fmt.Errorf("no matching client alias found: %s", clientAlias)
	}

	creatorID := searchResp.Items[0].ID
	if creatorID == 0 {
		return models.WialonGeneral{}, fmt.Errorf("invalid creator ID for client alias: %s", clientAlias)
	}

	// Step 2: Create the user
	tempPassword := generateRandomPassword(email, 12)
	createParams := map[string]interface{}{
		"creatorId": creatorID,
		"name":      email,
		"password":  tempPassword,
		"dataFlags": 1,
	}

	createResp, err := WialonAPIPost(ctx, "core/create_user", createParams)
	if err != nil {
		return models.WialonGeneral{}, fmt.Errorf("failed to create user: %w", err)
	}

	// Parse response to extract user ID
	var createResult struct {
		Item struct {
			ID   int64  `json:"id"`
			Name string `json:"nm"`
		} `json:"item"`
		Flags  uint   `json:"flags"`
		Error  int    `json:"error,omitempty"`
		Reason string `json:"reason,omitempty"`
	}
	if err := json.Unmarshal(createResp, &createResult); err != nil {
		return models.WialonGeneral{}, fmt.Errorf("failed to parse create_user response: %w", err)
	}

	if createResult.Error != 0 {
		return models.WialonGeneral{}, fmt.Errorf("create_user failed: %s (error=%d)", createResult.Reason, createResult.Error)
	}

	userID := createResult.Item.ID
	if userID == 0 {
		return models.WialonGeneral{}, fmt.Errorf("create_user did not return valid user ID: %s", string(createResp))
	}

	// Step 3: Update the user's email
	updateParams := map[string]interface{}{
		"itemId": userID,
		"name":   "email",
		"value":  email,
	}

	updateResp, err := WialonAPIPost(ctx, "item/update_custom_property", updateParams)
	if err != nil {
		return models.WialonGeneral{}, fmt.Errorf("failed to update user email: %w", err)
	}

	var updateResult struct {
		Error  int    `json:"error,omitempty"`
		Reason string `json:"reason,omitempty"`
	}
	if err := json.Unmarshal(updateResp, &updateResult); err == nil && updateResult.Error != 0 {
		return models.WialonGeneral{}, fmt.Errorf("email update failed: %s", updateResult.Reason)
	}

	// Return success
	return models.WialonGeneral{
		UserID:       fmt.Sprintf("%d", userID),
		ACL:          0,
		DACL:         0,
		TempPassword: tempPassword,
	}, nil
}

func (r *wialonRepository) BlockUserAccount(ctx context.Context, wialonUserID string, blockValue int) error {
	userID, err := strconv.Atoi(wialonUserID)
	if err != nil {
		return fmt.Errorf("invalid unit ID: %w", err)
	}

	statusFlag := constants.WIALON_USER_STATUS_DISABLE
	if blockValue == 0 {
		statusFlag = constants.WIALON_USER_STATUS_ENABLE
	}

	params := map[string]interface{}{
		"userId":    userID,
		"flags":     statusFlag, // 0 = enable, 1 = disable
		"flagsMask": 1,          // only apply login-related flag
	}

	body, err := WialonAPIPost(ctx, "user/update_user_flags", params)
	if err != nil {
		return fmt.Errorf("failed to update user status: %w", err)
	}

	var response struct {
		Error  int    `json:"error,omitempty"`
		Reason string `json:"reason,omitempty"`
	}
	if err := json.Unmarshal(body, &response); err == nil && response.Error != 0 {
		return fmt.Errorf("status update failed: %s", response.Reason)
	}

	return nil
}
