package models

import (
	"assetfindr/pkg/common/commonmodel"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type Attachment struct {
	commonmodel.ModelV2
	Number   string      `json:"number"`
	Label    null.String `gorm:"type:varchar(100);" json:"label"`
	Path     string      `gorm:"type:varchar(255);not null" json:"path"`
	FileType string      `gorm:"type:varchar(20);not null" json:"file_type"`

	AttachmentSources []AttachmentSource `json:"attachment_sources"`
}

func (a Attachment) TableName() string {
	return "sts_attachments"
}

func (a *Attachment) BeforeCreate(db *gorm.DB) error {
	a.SetUUID("att")
	a.ModelV2.BeforeCreate(db)
	return nil
}

func (a *Attachment) BeforeUpdate(db *gorm.DB) error {
	a.ModelV2.BeforeUpdate(db)
	return nil
}

type AttachmentCondition struct {
	Where   AttachmentWhere
	Columns []string
}

type AttachmentWhere struct {
	ID                 string
	ClientID           string
	ReferenceCode      string
	SourceReferenceID  string
	SourceReferenceIDs []string
	TargetReferenceID  string
}

type GetTicketListParam struct {
	commonmodel.ListRequest
	AttachmentCondition
}

type GetAttachmentByIDParam struct {
	ID   string
	Cond AttachmentCondition
}
