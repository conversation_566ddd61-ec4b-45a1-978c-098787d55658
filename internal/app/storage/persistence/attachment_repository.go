package persistence

import (
	"assetfindr/internal/app/storage/models"
	"assetfindr/internal/app/storage/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"context"

	"gorm.io/gorm"
)

type attachmentRepository struct{}

func NewAttachmentRepository() repository.AttachmentRepository {
	return &attachmentRepository{}
}

func enrichAttachmentQueryWithWhere(query *gorm.DB, where models.AttachmentWhere) {
	if where.ID != "" {
		query.Where("id = ?", where.ID)
	}

	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	}

	if where.ReferenceCode != "" {
		query.Where("reference_code = ?", where.ReferenceCode)
	}

	if where.SourceReferenceID != "" {
		query.Where("source_reference_id = ?", where.SourceReferenceID)
	}

	if len(where.SourceReferenceIDs) > 0 {
		query.Where("source_reference_id IN ?", where.SourceReferenceIDs)
	}

	if where.TargetReferenceID != "" {
		query.Where("target_reference_id = ?", where.TargetReferenceID)
	}
}

func (r *attachmentRepository) GetAttachments(ctx context.Context, dB database.DBI, condition models.AttachmentCondition) ([]models.Attachment, error) {
	attachments := []models.Attachment{}
	query := dB.GetTx().Model(&attachments)

	enrichAttachmentQueryWithWhere(query, condition.Where)

	query.Order("sts_attachments.updated_at DESC")

	err := query.Find(&attachments).Error
	if err != nil {
		return nil, err
	}

	return attachments, nil
}

func enrichAttachmentSourceQueryWithWhere(query *gorm.DB, where models.AttachmentWhere) {
	if where.ID != "" {
		query.Where("sts_attachments.id = ?", where.ID)
	}

	if where.ClientID != "" {
		query.Where("sts_attachments.client_id = ?", where.ClientID)
	}

	if where.ReferenceCode != "" {
		query.Where("sts_attachment_sources.reference_code = ?", where.ReferenceCode)
	}

	if where.SourceReferenceID != "" {
		query.Where("sts_attachment_sources.source_reference_id = ?", where.SourceReferenceID)
	}

	if len(where.SourceReferenceIDs) > 0 {
		query.Where("sts_attachment_sources.source_reference_id IN ?", where.SourceReferenceIDs)
	}

	if where.TargetReferenceID != "" {
		query.Where("sts_attachment_sources.target_reference_id = ?", where.TargetReferenceID)
	}
}

func (r *attachmentRepository) GetAttachmentsSources(ctx context.Context, dB database.DBI, condition models.AttachmentCondition) ([]models.AttachmentSource, error) {
	attachments := []models.AttachmentSource{}
	query := dB.GetTx().Model(&models.AttachmentSource{}).Preload("Attachment")

	enrichAttachmentSourceQueryWithWhere(query, condition.Where)
	query.Joins("JOIN sts_attachments ON sts_attachments.id = sts_attachment_sources.attachment_id AND sts_attachments.deleted_at IS NULL")

	err := query.Find(&attachments).Error
	if err != nil {
		return nil, err
	}

	return attachments, nil
}

func (r *attachmentRepository) GetAttachment(ctx context.Context, dB database.DBI, condition models.AttachmentCondition) (*models.Attachment, error) {
	attachment := models.Attachment{}
	query := dB.GetTx().Model(&models.Attachment{})

	enrichAttachmentQueryWithWhere(query, condition.Where)

	err := query.First(&attachment).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("attachment")
		}
		return nil, err
	}

	return &attachment, nil
}

func (s *attachmentRepository) CreateAttachments(ctx context.Context, dB database.DBI, attachments []models.Attachment) error {
	if len(attachments) == 0 {
		return nil
	}

	err := dB.GetTx().Create(&attachments).Error
	return err
}

func (s *attachmentRepository) CreateAttachmentSources(ctx context.Context, dB database.DBI, attachmentSources []models.AttachmentSource) error {
	if len(attachmentSources) == 0 {
		return nil
	}

	err := dB.GetTx().Create(&attachmentSources).Error
	return err
}

func (s *attachmentRepository) DeleteAttachments(ctx context.Context, dB database.DBI, attachmentIDs []string) error {
	if len(attachmentIDs) == 0 {
		return nil
	}

	return dB.GetTx().Delete(&models.Attachment{}, attachmentIDs).Error
}

func (r *attachmentRepository) GetAttachmentByID(ctx context.Context, dB database.DBI, param models.GetAttachmentByIDParam) (*models.Attachment, error) {
	attachment := models.Attachment{}
	query := dB.GetOrm().Model(&models.Attachment{})

	if param.Cond.Where.TargetReferenceID != "" {
		query.Where("target_reference_id = ?", param.Cond.Where.TargetReferenceID)
	}

	if param.Cond.Where.ClientID != "" {
		query.Where("client_id = ?", param.Cond.Where.ClientID)
	}

	query.Where("id = ?", param.ID)

	if err := query.First(&attachment).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("attachment")
		}
		return nil, err
	}

	return &attachment, nil
}

func (r *attachmentRepository) DeleteAttachment(ctx context.Context, dB database.DBI, id string) error {
	return dB.GetTx().
		Delete(&models.Attachment{}, "id = ?", id).
		Error
}

func (r *attachmentRepository) UpdateAttachment(ctx context.Context, dB database.DBI, id string, attachment *models.Attachment) error {
	return dB.GetTx().
		Model(&models.Attachment{}).
		Where("id = ?", id).
		Updates(attachment).
		Error
}
