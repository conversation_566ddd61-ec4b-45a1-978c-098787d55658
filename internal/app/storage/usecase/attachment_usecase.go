package usecase

import (
	"assetfindr/internal/app/storage/constants"
	"assetfindr/internal/app/storage/dtos"
	"assetfindr/internal/app/storage/models"
	"assetfindr/internal/app/storage/repository"
	internalConstants "assetfindr/internal/constants"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"

	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers"
	"assetfindr/pkg/common/helpers/authhelpers"
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/spf13/viper"
)

type AttachmentUseCase struct {
	DB                   database.DBUsecase
	AttachmentRepository repository.AttachmentRepository
	StorageRepository    repository.StorageRepository
}

func NewAttachmentUseCase(DB database.DBUsecase, attachmentRepo repository.AttachmentRepository, storageRepo repository.StorageRepository) *AttachmentUseCase {
	return &AttachmentUseCase{
		DB:                   DB,
		AttachmentRepository: attachmentRepo,
		StorageRepository:    storageRepo,
	}
}

func generateDestinationPath(clientID, referenceCode, prevPath string) string {
	return clientID + "/" +
		referenceCode + "/" +
		strings.Trim(prevPath, constants.TEMP_USER_UPLOAD_PREFIX)
}

func (uc *AttachmentUseCase) GetAttachments(ctx context.Context, req dtos.GetAttachmentsReq) ([]dtos.GetAttachmentsResp, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	attachmentsSources, err := uc.AttachmentRepository.GetAttachmentsSources(ctx, uc.DB.DB(), models.AttachmentCondition{
		Where: models.AttachmentWhere{
			ClientID:           claim.GetLoggedInClientID(),
			ReferenceCode:      req.ReferenceCode,
			SourceReferenceID:  req.SourceReferenceID,
			SourceReferenceIDs: req.SourceReferenceIDs,
			TargetReferenceID:  req.TargetReferenceID,
		},
	})
	if err != nil {
		return nil, err
	}

	resp := make([]dtos.GetAttachmentsResp, 0, len(attachmentsSources))
	for _, attachmentsSource := range attachmentsSources {
		url, _ := helpers.GenerateCloudStorageSignedURL(attachmentsSource.Attachment.Path, time.Duration(24))
		resp = append(resp, dtos.GetAttachmentsResp{
			Id:                attachmentsSource.Attachment.ID,
			Number:            attachmentsSource.Attachment.Number,
			CreatedAt:         attachmentsSource.Attachment.CreatedAt,
			Label:             attachmentsSource.Attachment.Label,
			ReferenceCode:     attachmentsSource.ReferenceCode,
			SourceReferenceID: attachmentsSource.SourceReferenceID,
			TargetReferenceID: attachmentsSource.TargetReferenceID,
			Path:              url,
			FileType:          attachmentsSource.Attachment.FileType,
		})
	}

	return resp, nil
}

func GeneratePublicGalleryJWTSignedToken(clientID string) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, authhelpers.PublicGaleryJwtTokenClaims{
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: time.Now().Add(60 * 24 * time.Hour).Unix(),
		},
		LoggedInClientID: clientID,
	})
	jwtKey := []byte(viper.GetString(internalConstants.PUBLIC_GALLERY_JWT_SECRET_KEY))
	signedToken, err := token.SignedString(jwtKey)
	if err != nil {
		return "", err
	}

	return signedToken, nil
}

func (uc *AttachmentUseCase) AttachmentPublicLinks(ctx context.Context, ids []string, clientID, clientAlias string) ([]string, error) {
	apiURL := internalConstants.STAGING_URL
	if appEnv := os.Getenv(internalConstants.ENV_APP_ENV); appEnv == "production" {
		apiURL = clientAlias + internalConstants.BASE_URL
	} else if appEnv == "sandbox" {
		apiURL = internalConstants.SANDBOX_URL
	}

	signedToken, err := GeneratePublicGalleryJWTSignedToken(clientID)
	if err != nil {
		return nil, err
	}

	res := make([]string, 0, len(ids))
	for _, id := range ids {
		res = append(res, fmt.Sprintf("https://%s/#/gallery?attachment_id=%s&token=%s", apiURL, id, signedToken))
	}

	return res, nil
}

func (uc *AttachmentUseCase) PhotoPublicLink(ctx context.Context, photo string, clientID, clientAlias string) (string, error) {
	apiURL := internalConstants.STAGING_URL
	if appEnv := os.Getenv(internalConstants.ENV_APP_ENV); appEnv == "production" {
		apiURL = clientAlias + internalConstants.BASE_URL
	} else if appEnv == "sandbox" {
		apiURL = internalConstants.SANDBOX_URL
	}

	signedToken, err := GeneratePublicGalleryJWTSignedToken(clientID)
	if err != nil {
		return "", err
	}

	res := fmt.Sprintf("https://%s/#/gallery?path=%s&token=%s", apiURL, photo, signedToken)
	return res, nil
}

func (uc *AttachmentUseCase) MoveUserPhotoStorage(ctx context.Context, clientID, prevPath string) (string, error) {
	destinationPath := generateDestinationPath(clientID, "USER_PROFILE", prevPath)
	_, err := uc.StorageRepository.MoveFile(ctx, prevPath, destinationPath)
	if err != nil {
		return "", err
	}

	return destinationPath, nil
}

func (uc *AttachmentUseCase) MoveProductPhotoStorage(ctx context.Context, clientID, prevPath string) (string, error) {
	destinationPath := generateDestinationPath(clientID, "PRODUCT_PHOTO", prevPath)
	_, err := uc.StorageRepository.MoveFile(ctx, prevPath, destinationPath)
	if err != nil {
		return "", err
	}

	return destinationPath, nil
}

func (uc *AttachmentUseCase) MoveGeneralPhotoStorage(ctx context.Context, refCode, clientID, prevPath string) (string, error) {
	destinationPath := generateDestinationPath(clientID, refCode, prevPath)
	_, err := uc.StorageRepository.MoveFile(ctx, prevPath, destinationPath)
	if err != nil {
		return "", err
	}

	return destinationPath, nil
}

func (uc *AttachmentUseCase) MoveClientPhotoStorage(ctx context.Context, clientID, prevPath string) (string, error) {
	destinationPath := generateDestinationPath(clientID, "CLIENT_PROFILE", prevPath)
	_, err := uc.StorageRepository.MoveFile(ctx, prevPath, destinationPath)
	if err != nil {
		return "", err
	}

	return destinationPath, nil
}

func (uc *AttachmentUseCase) MoveAssetPhotoStorage(ctx context.Context, clientID, prevPath string) (string, error) {
	destinationPath := generateDestinationPath(clientID, "ASSET_PHOTO", prevPath)
	_, err := uc.StorageRepository.MoveFile(ctx, prevPath, destinationPath)
	if err != nil {
		return "", err
	}

	return destinationPath, nil
}

func (uc *AttachmentUseCase) MovePhotoStorage(ctx context.Context, clientID, refCode, prevPath string) (string, error) {
	destinationPath := generateDestinationPath(clientID, refCode, prevPath)
	_, err := uc.StorageRepository.MoveFile(ctx, prevPath, destinationPath)
	if err != nil {
		return "", err
	}

	return destinationPath, nil
}

func (uc *AttachmentUseCase) GetFileSignedURL(ctx context.Context, filePath string) (string, error) {
	signedURL, err := uc.StorageRepository.GetFileSignedURL(ctx, filePath, time.Now().Add(24*time.Hour))
	if err != nil {
		return "", err
	}

	return signedURL, nil
}

func (uc *AttachmentUseCase) CreateAttachmentsPhotosV2(ctx context.Context, param dtos.UpsertAttachmentReq) ([]models.Attachment, error) {
	attachments := make([]models.Attachment, 0, len(param.Photos))
	for _, photo := range param.Photos {
		destinationPath := generateDestinationPath(param.ClientID, param.ReferenceCode, photo.Path)
		contentType, err := uc.StorageRepository.MoveFile(ctx, photo.Path, destinationPath)
		if err != nil {
			return nil, err
		}

		attachment := models.Attachment{
			Label:             photo.Label,
			Path:              destinationPath,
			FileType:          contentType,
			AttachmentSources: []models.AttachmentSource{},
		}

		for i := range param.AttachmentSources {
			attachment.AttachmentSources = append(attachment.AttachmentSources, models.AttachmentSource{
				ReferenceCode:     param.AttachmentSources[i].ReferenceCode,
				SourceReferenceID: param.AttachmentSources[i].SourceReferenceID,
				TargetReferenceID: param.AttachmentSources[i].TargetReferenceID,
			})
		}

		if param.ReferenceCode != "" && param.SourceReferenceID != "" {
			attachment.AttachmentSources = append(attachment.AttachmentSources, models.AttachmentSource{
				ReferenceCode:     param.ReferenceCode,
				SourceReferenceID: param.SourceReferenceID,
				TargetReferenceID: param.TargetReferenceID,
			})

		}

		attachments = append(attachments, attachment)
	}

	err := uc.AttachmentRepository.CreateAttachments(ctx, uc.DB.WithCtx(ctx).DB(), attachments)
	if err != nil {
		return nil, fmt.Errorf("error when create attachments: %w", err)
	}

	return attachments, nil
}

func (uc *AttachmentUseCase) UpdateAttachmentPhotos(ctx context.Context, param dtos.UpsertAttachmentReq) ([]models.Attachment, error) {
	newAttachments := make([]models.Attachment, 0, len(param.Photos))
	needToDeleteAttachmentIDs := make([]string, 0, len(param.Photos))

	for _, photo := range param.Photos {
		if photo.IsDelete {
			needToDeleteAttachmentIDs = append(needToDeleteAttachmentIDs, photo.ID)
			continue
		}

		if !photo.IsNew() {
			continue
		}

		destinationPath := generateDestinationPath(param.ClientID,
			constants.ATTACHMENT_REFERENCE_CODE_ASSET, photo.Path)
		contentType, err := uc.StorageRepository.MoveFile(ctx, photo.Path, destinationPath)
		if err != nil {
			return nil, err
		}

		newAttachment := models.Attachment{
			Label:             photo.Label,
			Path:              destinationPath,
			FileType:          contentType,
			AttachmentSources: []models.AttachmentSource{},
		}
		for i := range param.AttachmentSources {
			newAttachment.AttachmentSources = append(newAttachment.AttachmentSources, models.AttachmentSource{
				ReferenceCode:     param.AttachmentSources[i].ReferenceCode,
				SourceReferenceID: param.AttachmentSources[i].SourceReferenceID,
				TargetReferenceID: param.AttachmentSources[i].TargetReferenceID,
			})
		}

		if param.ReferenceCode != "" && param.SourceReferenceID != "" {
			newAttachment.AttachmentSources = append(newAttachment.AttachmentSources, models.AttachmentSource{
				ReferenceCode:     param.ReferenceCode,
				SourceReferenceID: param.SourceReferenceID,
				TargetReferenceID: param.TargetReferenceID,
			})

		}
		newAttachments = append(newAttachments, newAttachment)
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	err = uc.AttachmentRepository.CreateAttachments(ctx, tx.DB(), newAttachments)
	if err != nil {
		return nil, fmt.Errorf("error when create attachments: %w", err)
	}

	err = uc.AttachmentRepository.DeleteAttachments(ctx, tx.DB(), needToDeleteAttachmentIDs)
	if err != nil {
		return nil, fmt.Errorf("error when delete attachments: %w", err)
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return newAttachments, nil
}

func (uc *AttachmentUseCase) DeleteAttachments(ctx context.Context, id string) (*commonmodel.DeleteResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	_, err = uc.AttachmentRepository.GetAttachment(ctx, uc.DB.DB(), models.AttachmentCondition{
		Where: models.AttachmentWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
		Columns: []string{},
	})
	if err != nil {
		return nil, err
	}

	err = uc.AttachmentRepository.DeleteAttachment(ctx, uc.DB.DB(), id)
	if err != nil {
		return nil, err
	}

	return &commonmodel.DeleteResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
	}, nil
}

func (uc *AttachmentUseCase) CreateAttachment(ctx context.Context, req dtos.CreateAttachmentReq) (*models.Attachment, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}
	clientId := claim.GetLoggedInClientID()

	destinationPath := generateDestinationPath(clientId, req.ReferenceCode, req.Path)
	contentType, err := uc.StorageRepository.MoveFile(ctx, req.Path, destinationPath)
	if err != nil {
		return nil, err
	}

	attachment := models.Attachment{
		Label:    req.Label,
		Path:     destinationPath,
		FileType: contentType,
		AttachmentSources: []models.AttachmentSource{
			{
				ReferenceCode:     req.ReferenceCode,
				SourceReferenceID: req.SourceReferenceID,
				TargetReferenceID: req.TargetReferenceID,
			},
		},
	}

	err = uc.AttachmentRepository.CreateAttachments(ctx, uc.DB.WithCtx(ctx).DB(), []models.Attachment{
		attachment,
	})
	if err != nil {
		return nil, fmt.Errorf("error when create attachments: %w", err)
	}

	return &attachment, nil

}

func (uc *AttachmentUseCase) UpdateAttachmentLabel(ctx context.Context, id string, req dtos.UpdateAttachmentReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	_, err = uc.AttachmentRepository.GetAttachment(ctx, uc.DB.DB(), models.AttachmentCondition{
		Where: models.AttachmentWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	err = uc.AttachmentRepository.UpdateAttachment(ctx, uc.DB.DB(), id, &models.Attachment{
		Label: req.Label,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *AttachmentUseCase) GetPublicAttachment(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetPublicGalleryClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	attachment, err := uc.AttachmentRepository.GetAttachment(ctx, uc.DB.DB(), models.AttachmentCondition{
		Where: models.AttachmentWhere{
			ID:       id,
			ClientID: claim.LoggedInClientID,
		},
	})
	if err != nil {
		return nil, err
	}

	signedUrl, err := uc.StorageRepository.GetFileSignedURL(ctx, attachment.Path, time.Now().Add(7*24*time.Hour))
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        signedUrl,
	}, nil
}

func (uc *AttachmentUseCase) GetPublicPhoto(ctx context.Context, req dtos.GetPublicPhotoReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetPublicGalleryClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	if !strings.HasPrefix(req.Path, claim.LoggedInClientID) {
		return nil, errorhandler.ErrBadRequest("Invalid path")
	}

	signedUrl, err := uc.StorageRepository.GetFileSignedURL(ctx, req.Path, time.Now().Add(7*24*time.Hour))
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: req.Path,
		Data:        signedUrl,
	}, nil
}
