package usecase

import (
	assetServiceModels "assetfindr/internal/app/asset/models"
	assetRepo "assetfindr/internal/app/asset/repository"
	assetServiceUsecase "assetfindr/internal/app/asset/usecase"
	notifConstants "assetfindr/internal/app/notification/constants"
	notificationDtos "assetfindr/internal/app/notification/dtos"
	notificationUsecase "assetfindr/internal/app/notification/usecase"
	storageRepository "assetfindr/internal/app/storage/repository"
	"assetfindr/internal/app/task/constants"
	"assetfindr/internal/app/task/dtos"
	"assetfindr/internal/app/task/models"
	"assetfindr/internal/app/task/repository"

	repoOrder "assetfindr/internal/app/inventory/repository"
	userIdentityModel "assetfindr/internal/app/user-identity/models"
	userIdentityRepository "assetfindr/internal/app/user-identity/repository"
	internalConstants "assetfindr/internal/constants"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/contexthelpers"
	"assetfindr/pkg/common/helpers/sqlhelpers"
	"assetfindr/pkg/common/helpers/timehelpers"
	"assetfindr/pkg/common/helpers/tmplhelpers"
	"context"
	"fmt"
	"html/template"
	"log"
	"os"
	"strings"
	"time"

	storageConstants "assetfindr/internal/app/storage/constants"
	storageDtos "assetfindr/internal/app/storage/dtos"
	storageUseCase "assetfindr/internal/app/storage/usecase"

	financeConstants "assetfindr/internal/app/finance/constants"
	financeDtos "assetfindr/internal/app/finance/dtos"
	financeUsecase "assetfindr/internal/app/finance/usecase"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
)

type TicketUseCase struct {
	DB                  database.DBUsecase
	TicketRepository    repository.TicketRepository
	UserRepository      userIdentityRepository.UserRepository
	AssetRepository     assetRepo.AssetRepository
	assetVehicleRepo    assetRepo.AssetVehicleRepository
	AssetUseCase        *assetServiceUsecase.AssetUseCase
	notifUseCase        *notificationUsecase.NotificationUseCase
	financeUseCase      financeUsecase.FinanceUseCase
	assetTyreRepo       assetRepo.AssetTyreRepository
	attachmentUseCase   *storageUseCase.AttachmentUseCase
	assetAssignmentRepo assetRepo.AssetAssignmentRepository
	departmentRepo      userIdentityRepository.DepartmentRepository
	assetComponentRepo  assetRepo.AssetComponentRepository
	assetInspectionRepo assetRepo.AssetInspectionRepository
	locationRepo        assetRepo.LocationRepository
	OrderRepo           repoOrder.OrderRepository
	storageRepository   storageRepository.StorageRepository
}

func NewTicketUseCase(
	DB database.DBUsecase,
	ticketRepo repository.TicketRepository,
	userRepository userIdentityRepository.UserRepository,
	AssetRepository assetRepo.AssetRepository,
	financeUseCase financeUsecase.FinanceUseCase,
	assetTyreRepository assetRepo.AssetTyreRepository,
	attachmentUseCase *storageUseCase.AttachmentUseCase,
	assetVehicleRepo assetRepo.AssetVehicleRepository,
	assetAssignmentRepo assetRepo.AssetAssignmentRepository,
	departmentRepo userIdentityRepository.DepartmentRepository,
	assetComponentRepo assetRepo.AssetComponentRepository,
	assetInspectionRepo assetRepo.AssetInspectionRepository,
	locationRepo assetRepo.LocationRepository,
	orderRepo repoOrder.OrderRepository,
	storageRepository storageRepository.StorageRepository,
) *TicketUseCase {
	return &TicketUseCase{
		DB:                  DB,
		TicketRepository:    ticketRepo,
		UserRepository:      userRepository,
		AssetRepository:     AssetRepository,
		financeUseCase:      financeUseCase,
		assetTyreRepo:       assetTyreRepository,
		attachmentUseCase:   attachmentUseCase,
		assetVehicleRepo:    assetVehicleRepo,
		assetAssignmentRepo: assetAssignmentRepo,
		departmentRepo:      departmentRepo,
		assetComponentRepo:  assetComponentRepo,
		assetInspectionRepo: assetInspectionRepo,
		locationRepo:        locationRepo,
		OrderRepo:           orderRepo,
		storageRepository:   storageRepository,
	}
}

func (uc *TicketUseCase) SetNotifUseCase(notifUseCase *notificationUsecase.NotificationUseCase) {
	uc.notifUseCase = notifUseCase
}

func (uc *TicketUseCase) SetAssetUseCase(assetUseCase *assetServiceUsecase.AssetUseCase) {
	uc.AssetUseCase = assetUseCase
}

func (uc *TicketUseCase) isWOViewAll(permissionRight *authhelpers.PermissionRight) bool {
	if permissionRight == nil || len(permissionRight.PermissionCodes) == 0 {
		return false
	}
	for _, val := range permissionRight.PermissionCodes {
		if val == constants.TICKET_PERMISSION_VIEW_ALL_LIST_TICKET {
			return true
		}
	}
	return false
}

func (uc *TicketUseCase) GetTicketsV2(ctx context.Context, req dtos.TicketListReq) (commonmodel.ListResponse, error) {
	defer func() {
		if r := recover(); r != nil {
			commonlogger.Errorf("recovered from ", r)
		}
	}()

	ticketResponses := commonmodel.ListResponse{
		PageSize: req.PageSize,
		PageNo:   req.PageNo,
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return ticketResponses, err
	}

	startDate, _ := parseScheduleDate(req.StartDate, false)
	endDate, _ := parseScheduleDate(req.EndDate, true)

	whereCondition := models.TicketWhere{
		ClientID:            claim.GetLoggedInClientID(),
		TicketReferenceCode: "",
		ReferenceID:         req.ReferenceID,
		StatusCodes:         req.StatusCodes,
		Subjects:            req.Subjects,
		StartDate:           startDate,
		EndDate:             endDate,
		DepartmentID:        req.DepartmentID,
		Categories:          req.Categories,
		ExcludeCategories:   req.ExcludeCategories,
		HasCost:             req.HasCost,
		IsArchived:          null.BoolFrom(req.IsArchived),
		RequesterUserIDs:    req.RequesterUserIDs,
		AssignedToUserIDs:   req.AssignedToUserIDs,
		RequestedByUserIDs:  req.RequestedByUserIDs,
	}

	if req.PartnerOwnerID != "" {
		assets, err := uc.AssetRepository.GetAssets(ctx, uc.DB.DB(), assetServiceModels.AssetCondition{
			Where: assetServiceModels.AssetWhere{
				PartnerOwnerID: req.PartnerOwnerID,
				ClientID:       claim.GetLoggedInClientID(),
			},
			Columns: []string{"id"},
		})
		if err != nil {
			return ticketResponses, err
		}

		assetIDs := make([]string, 0, len(assets))
		for i := range assets {
			assetIDs = append(assetIDs, assets[i].ID)
		}

		if len(assetIDs) == 0 {
			return ticketResponses, nil
		}

		whereCondition.ReferenceIDs = append(whereCondition.ReferenceIDs, assetIDs...)
	}

	if req.AssignedToMe && req.CreatedByMe {
		whereCondition.RequesterAndAssignedUserID = claim.UserID
	} else {
		if req.CreatedByMe {
			whereCondition.RequesterUserID = claim.UserID
		}
		if req.AssignedToMe {
			whereCondition.AssignedUserID = claim.UserID
		}
	}

	workOrderGroup, _ := claim.GetPermissionCategory("WORK_ORDER")
	isWOADMIN := uc.isWOViewAll(workOrderGroup)

	/*
		WORKSHOP CONFIG
		Mekaniks able to see work order which the inspection being assign to them
	*/
	if !isWOADMIN {
		// Get ticket id from inspections
		inspections, err := uc.assetInspectionRepo.GetAssetInspectionByAssignedUserId(ctx, uc.DB.DB(), claim.UserID, "WORK_ORDER")
		if err != nil {
			return ticketResponses, err
		}
		ticketIds := []string{}

		for _, inspection := range inspections {
			ticketIds = append(ticketIds, inspection.ReferenceID)
		}

		whereCondition.IDs = ticketIds
	}

	/*
		NOT ADMIN CONFIG
		If login user is not admin then they only able to see
		work order which assign or created by them
	*/
	if !isWOADMIN {
		whereCondition.RequesterOrAssignedUserID = claim.UserID
	}

	if req.IsDueSoon {
		whereCondition.DueSoon = true
	}
	if req.IsOverDue {
		whereCondition.Overdue = true
	}
	if req.IsDueSoon || req.IsOverDue {
		whereCondition.ExcludeStatusCodes = []string{constants.TICKET_STATUS_CODE_CANCELED, constants.TICKET_STATUS_CODE_CLOSED, constants.TICKET_STATUS_CODE_RESOLVED}
	}

	if req.IsUpComingServiceReminder {
		whereCondition.Categories = []string{constants.TICKET_CATEGORY_CODE_CEK_BERKALA, constants.TICKET_CATEGORY_CODE_CEK_KOPLING, constants.TICKET_CATEGORY_CODE_CEK_REM, constants.TICKET_CATEGORY_CODE_CEK_TEKANAN_BAN}
		whereCondition.ExcludeStatusCodes = []string{constants.TICKET_STATUS_CODE_CANCELED, constants.TICKET_STATUS_CODE_CLOSED, constants.TICKET_STATUS_CODE_RESOLVED}
		whereCondition.IsUpcomingServiceReminder = true
	}

	if req.IsOverdueServiceReminder {
		whereCondition.Categories = []string{constants.TICKET_CATEGORY_CODE_CEK_BERKALA, constants.TICKET_CATEGORY_CODE_CEK_KOPLING, constants.TICKET_CATEGORY_CODE_CEK_REM, constants.TICKET_CATEGORY_CODE_CEK_TEKANAN_BAN}
		whereCondition.ExcludeStatusCodes = []string{constants.TICKET_STATUS_CODE_CANCELED, constants.TICKET_STATUS_CODE_CLOSED, constants.TICKET_STATUS_CODE_RESOLVED}
		whereCondition.IsOverdueServiceReminder = true
	}

	count, tickets, err := uc.TicketRepository.GetTicketList(ctx, uc.DB.DB(), models.GetTicketListParam{
		ListRequest: req.ListRequest,
		Cond: models.TicketCondition{
			Where: whereCondition,
			Preload: models.TicketPreload{
				Parents:       true,
				Childs:        true,
				SeverityLevel: true,

				TicketCategory: true,
			},
		},
	})
	if err != nil {
		return ticketResponses, err
	}

	if len(tickets) == 0 {
		return ticketResponses, nil
	}

	// Get user by Ids
	var userIds []string

	// Get Asset By ids
	var assetIds []string

	var departmentIds []string

	for _, ticket := range tickets {
		if ticket.RequesterUserID != "" {
			userIds = append(userIds, ticket.RequesterUserID)
		}

		if ticket.AssignedToUserID.String != "" {
			userIds = append(userIds, ticket.AssignedToUserID.String)
		}

		if ticket.ReferenceID != "" {
			assetIds = append(assetIds, ticket.ReferenceID)
		}

		if ticket.DepartmentID != "" {
			departmentIds = append(departmentIds, ticket.DepartmentID)
		}
	}

	usersMapById := map[string]userIdentityModel.User{}

	err = uc.UserRepository.GetUsersInMapByIds(ctx, uc.DB.DB(), &usersMapById, userIds)
	if err != nil {
		commonlogger.Errorf("Error in getting users by user ids from identity service", err)
		return ticketResponses, err
	}

	assetsMapById := map[string]assetServiceModels.Asset{}

	if len(assetIds) > 0 {
		err = uc.AssetUseCase.GetAssetsMapByID(ctx, &assetsMapById, assetIds)
		if err != nil {
			commonlogger.Errorf("Error in getting users by user ids from identity service", err)
			return ticketResponses, err
		}
	}

	departments := []userIdentityModel.Department{}
	departmentsMapById := map[string]userIdentityModel.Department{}
	if len(departmentIds) > 0 {
		err = uc.departmentRepo.GetDepartmentsByIds(ctx, uc.DB.DB(), &departments, departmentIds)
		if err != nil {
			commonlogger.Errorf("Error in getting departments by department ids from identity service", err)
			return ticketResponses, err
		}
		for _, val := range departments {
			departmentsMapById[val.ID] = val
		}
	}

	ticketResponses.Data = dtos.BuildTksTicketResponse(tickets, usersMapById, assetsMapById, departmentsMapById)
	ticketResponses.TotalRecords = count

	return ticketResponses, nil
}

func (uc *TicketUseCase) GetTicketCount(ctx context.Context, refId string, parameters []string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	whereCondition := models.TicketWhere{
		ClientID:    claim.GetLoggedInClientID(),
		ReferenceID: refId,
	}

	workOrderGroup, _ := claim.GetPermissionCategory(constants.TICKET_PERMISSION_WORK_ORDER)
	isWOViewAll := uc.isWOViewAll(workOrderGroup)

	if !isWOViewAll {
		nonViewAllCondition, err := uc.GenerateTicketNonViewAllCondition(ctx, claim)
		if err != nil {
			return nil, err
		}

		whereCondition.NonTicketAdminCondition = nonViewAllCondition
	}

	resp := dtos.TicketCountResp{
		Open:                    0,
		Overdue:                 0,
		DueSoon:                 0,
		HighPriority:            0,
		ResolvedIn7Days:         0,
		CreatedByMe:             0,
		AssetOverdueWorkOrder:   0,
		UpcomingServiceReminder: 0,
		OverdueServiceReminder:  0,
	}

	includeOpen := false
	includeOverdue := false
	includeDueSoon := false
	includeHighPriority := false
	includeResolvedIn7Days := false
	includeCreatedByMe := false
	includeAssetOverdueWorkOrder := false
	includeUpcomingServiceReminder := false
	includeOverdueServiceReminder := false
	for _, parameter := range parameters {
		switch strings.ToUpper(parameter) {
		case constants.TICKET_COUNT_PARAMETER_OPEN:
			includeOpen = true
		case constants.TICKET_COUNT_PARAMETER_OVERDUE:
			includeOverdue = true
		case constants.TICKET_COUNT_PARAMETER_DUESOON:
			includeDueSoon = true
		case constants.TICKET_COUNT_PARAMETER_HIGH_PRIORITY:
			includeHighPriority = true
		case constants.TICKET_COUNT_PARAMETER_RESOLVED_IN_7_DAYS:
			includeResolvedIn7Days = true
		case constants.TICKET_COUNT_PARAMETER_CREATED_BY_ME:
			includeCreatedByMe = true
		case constants.TICKET_COUNT_PARAMETER_ASSET_OVERDUE_WORK_ORDER:
			includeAssetOverdueWorkOrder = true
		case constants.TICKET_COUNT_PARAMETER_UP_COMING_SERVICE_REMINDER:
			includeUpcomingServiceReminder = true
		case constants.TICKET_COUNT_PARAMETER_OVERDUE_SERVICE_REMINDER:
			includeOverdueServiceReminder = true
		}
	}

	if includeOpen {
		copyWhereCond := whereCondition
		copyWhereCond.StatusCodes = []string{constants.TICKET_STATUS_CODE_OPEN}
		countOpenTicket, err := uc.TicketRepository.CountTicket(ctx, uc.DB.DB(), models.TicketCondition{
			Where: copyWhereCond,
		})
		if err != nil {
			return nil, err
		}

		resp.Open = countOpenTicket
	}

	if includeOverdue {
		copyWhereCond := whereCondition
		copyWhereCond.Overdue = true
		copyWhereCond.ExcludeStatusCodes = []string{constants.TICKET_STATUS_CODE_CANCELED, constants.TICKET_STATUS_CODE_CLOSED, constants.TICKET_STATUS_CODE_RESOLVED}
		countOverdue, err := uc.TicketRepository.CountTicket(ctx, uc.DB.DB(), models.TicketCondition{
			Where: copyWhereCond,
		})
		if err != nil {
			return nil, err
		}

		resp.Overdue = countOverdue
	}

	if includeDueSoon {
		copyWhereCond := whereCondition
		copyWhereCond.DueSoon = true
		copyWhereCond.ExcludeStatusCodes = []string{constants.TICKET_STATUS_CODE_CANCELED, constants.TICKET_STATUS_CODE_CLOSED, constants.TICKET_STATUS_CODE_RESOLVED}
		countDueSoon, err := uc.TicketRepository.CountTicket(ctx, uc.DB.DB(), models.TicketCondition{
			Where: copyWhereCond,
		})
		if err != nil {
			return nil, err
		}

		resp.DueSoon = countDueSoon
	}

	if includeHighPriority {
		copyWhereCond := whereCondition
		copyWhereCond.HighPriority = true
		copyWhereCond.ExcludeStatusCodes = []string{constants.TICKET_STATUS_CODE_CANCELED, constants.TICKET_STATUS_CODE_CLOSED, constants.TICKET_STATUS_CODE_RESOLVED}
		countHighPriority, err := uc.TicketRepository.CountTicket(ctx, uc.DB.DB(), models.TicketCondition{
			Where: copyWhereCond,
		})
		if err != nil {
			return nil, err
		}

		resp.HighPriority = countHighPriority
	}

	if includeResolvedIn7Days {
		copyWhereCond := whereCondition
		copyWhereCond.ResolvedIn7Days = true
		copyWhereCond.StatusCodes = []string{constants.TICKET_STATUS_CODE_RESOLVED, constants.TICKET_STATUS_CODE_CLOSED}
		countResolvedIn7Days, err := uc.TicketRepository.CountTicket(ctx, uc.DB.DB(), models.TicketCondition{
			Where: copyWhereCond,
		})
		if err != nil {
			return nil, err
		}

		resp.ResolvedIn7Days = countResolvedIn7Days
	}

	if includeCreatedByMe {
		copyWhereCond := whereCondition
		copyWhereCond.RequesterUserID = claim.UserID
		countCreatedByMe, err := uc.TicketRepository.CountTicket(ctx, uc.DB.DB(), models.TicketCondition{
			Where: copyWhereCond,
		})
		if err != nil {
			return nil, err
		}

		resp.CreatedByMe = countCreatedByMe
	}

	if includeAssetOverdueWorkOrder {
		copyWhereCond := whereCondition
		copyWhereCond.Overdue = true
		copyWhereCond.ExcludeStatusCodes = []string{constants.TICKET_STATUS_CODE_CANCELED, constants.TICKET_STATUS_CODE_CLOSED, constants.TICKET_STATUS_CODE_RESOLVED}
		overdueTickets, err := uc.TicketRepository.GetTicketsV2(ctx, uc.DB.DB(), models.TicketCondition{
			Where: copyWhereCond,
		})
		if err != nil {
			return nil, err
		}

		overdueTicketAssetIds := map[string]struct{}{}
		countAssetOverdueWorkOrder := 0
		for _, overdueTicket := range overdueTickets {
			if _, ok := overdueTicketAssetIds[overdueTicket.ReferenceID]; !ok {
				overdueTicketAssetIds[overdueTicket.ReferenceID] = struct{}{}
				countAssetOverdueWorkOrder++
			}
		}

		resp.AssetOverdueWorkOrder = countAssetOverdueWorkOrder
	}

	if includeUpcomingServiceReminder {
		copyWhereCond := whereCondition
		copyWhereCond.Categories = []string{constants.TICKET_CATEGORY_CODE_CEK_BERKALA, constants.TICKET_CATEGORY_CODE_CEK_KOPLING, constants.TICKET_CATEGORY_CODE_CEK_REM, constants.TICKET_CATEGORY_CODE_CEK_TEKANAN_BAN}
		copyWhereCond.ExcludeStatusCodes = []string{constants.TICKET_STATUS_CODE_CANCELED, constants.TICKET_STATUS_CODE_CLOSED, constants.TICKET_STATUS_CODE_RESOLVED}
		copyWhereCond.IsUpcomingServiceReminder = true
		countUpcomingServiceReminder, err := uc.TicketRepository.CountTicket(ctx, uc.DB.DB(), models.TicketCondition{
			Where: copyWhereCond,
		})
		if err != nil {
			return nil, err
		}

		resp.UpcomingServiceReminder = countUpcomingServiceReminder
	}

	if includeOverdueServiceReminder {
		copyWhereCond := whereCondition
		copyWhereCond.Categories = []string{constants.TICKET_CATEGORY_CODE_CEK_BERKALA, constants.TICKET_CATEGORY_CODE_CEK_KOPLING, constants.TICKET_CATEGORY_CODE_CEK_REM, constants.TICKET_CATEGORY_CODE_CEK_TEKANAN_BAN}
		copyWhereCond.ExcludeStatusCodes = []string{constants.TICKET_STATUS_CODE_CANCELED, constants.TICKET_STATUS_CODE_CLOSED, constants.TICKET_STATUS_CODE_RESOLVED}
		copyWhereCond.IsOverdueServiceReminder = true
		countOverdusServiceReminder, err := uc.TicketRepository.CountTicket(ctx, uc.DB.DB(), models.TicketCondition{
			Where: copyWhereCond,
		})
		if err != nil {
			return nil, err
		}

		resp.OverdueServiceReminder = countOverdusServiceReminder
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: refId,
		Data:        resp,
	}, nil
}

func parseDate(dateString string) *time.Time {
	layout := "2006-01-02 15:04:05"
	location, err := time.LoadLocation("Asia/Bangkok") // UTC+7
	if err != nil {
		commonlogger.Warnf("error parse date, load location %v", err)
		return nil
	}

	parsedDate, err := time.ParseInLocation(layout, dateString, location)
	if err != nil {
		commonlogger.Warnf("error parse date %v", err)
		return nil
	}
	return &parsedDate
}

func parseScheduleDate(dateString string, isEndDate bool) (time.Time, error) {
	layout := "2006-01-02"

	parsedDate, err := time.ParseInLocation(layout, dateString, time.Local)
	if err != nil {
		return time.Time{}, err
	}

	if isEndDate {
		parsedDate = time.Date(
			parsedDate.Year(),
			parsedDate.Month(),
			parsedDate.Day(),
			23, 59, 59, 0, // hour, minute, second, nanosecond
			parsedDate.Location(),
		)
	}

	return parsedDate, nil
}

func (uc *TicketUseCase) CreateTicket(ctx context.Context, createDTO *dtos.TicketResponse) (*models.Ticket, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	if createDTO.ParentTicketID != "" {
		_, err := uc.TicketRepository.GetTicket(ctx, uc.DB.DB(), models.TicketCondition{
			Where: models.TicketWhere{
				ID:       createDTO.ParentTicketID,
				ClientID: claim.GetLoggedInClientID(),
			},
		})
		if err != nil {
			return nil, err
		}
	}

	ticket := &models.Ticket{
		Subject:             createDTO.Subject,
		Description:         createDTO.Description,
		TicketCategoryCode:  createDTO.TicketCategoryCode,
		TicketReferenceCode: createDTO.TicketReferenceCode,
		ReferenceID:         createDTO.ReferenceID,
		SeverityLevelCode:   createDTO.SeverityLevelCode,
		RequesterUserID:     createDTO.RequesterUserID,
		AssignedToUserID:    createDTO.AssignedToUserID,
		StatusCode:          createDTO.StatusCode,
		Resolution:          createDTO.Resolution,
		Cost:                createDTO.Cost,
		DueDatetime:         parseDate(createDTO.DueDatetime),
		ScheduleDatetime:    parseDate(createDTO.ScheduleDatetime),
		PartnerOwnerID:      createDTO.PartnerOwnerID,
		PartnerOwnerNo:      createDTO.PartnerOwnerNo,
		PartnerOwnerName:    createDTO.PartnerOwnerName,
	}
	if ticket.DueDatetime != nil && ticket.DueDatetime.IsZero() {
		ticket.DueDatetime = nil
	}

	if ticket.ScheduleDatetime != nil && ticket.ScheduleDatetime.IsZero() {
		ticket.ScheduleDatetime = nil
	}

	// GET DEPARTMENT ID
	if createDTO.AssignedToUserID.Valid && createDTO.AssignedToUserID.String != "" {
		userClient, err := uc.UserRepository.GetUserClient(ctx, uc.DB.DB(), userIdentityModel.UserClientCondition{
			Where: userIdentityModel.UserClientWhere{
				UserID:   createDTO.AssignedToUserID.String,
				ClientID: claim.GetLoggedInClientID(),
			},
		})
		if err != nil {
			return nil, err
		}

		ticket.DepartmentID = userClient.DepartmentID.String
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer func() {
		tx.Rollback()
	}()

	err = uc.TicketRepository.CreateTicket(ctx, tx.DB(), ticket)
	if err != nil {
		return nil, err
	}

	if createDTO.ParentTicketID != "" {
		err = uc.TicketRepository.LinkedTicket(ctx, tx.DB(),
			createDTO.ParentTicketID,
			ticket.ID,
		)
		if err != nil {
			return nil, err
		}
	}

	_, err = uc.attachmentUseCase.CreateAttachmentsPhotosV2(ctx, storageDtos.UpsertAttachmentReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_TICKET,
		SourceReferenceID: ticket.ID,
		ClientID:          claim.GetLoggedInClientID(),
		Photos:            createDTO.Photos,
	})
	if err != nil {
		return nil, err
	}

	for _, contact := range createDTO.Contacts {
		contact.TicketID = ticket.ID
		err := uc.upsertTicketContact(ctx, tx.DB(), contact)
		if err != nil {
			return nil, err
		}
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	go uc.notifyAfterCreateTicket(contexthelpers.WithoutCancel(ctx), ticket)

	return ticket, nil
}

func (uc *TicketUseCase) notifyAfterCreateTicket(
	ctx context.Context,
	ticket *models.Ticket,
) {
	// Get Ticket Subject
	ticket, err := uc.TicketRepository.GetTicket(ctx, uc.DB.DB(), models.TicketCondition{
		Where: models.TicketWhere{
			ID: ticket.ID,
		},
		Preload: models.TicketPreload{
			TicketCategory: true,
		},
	})
	if err != nil {
		commonlogger.Warnf("Error in getting ticket preload", err)
		return
	}

	assetName := ""
	if ticket.ReferenceID != constants.GENERAL_TICKET_REFERENCE_ID {
		asset, err := uc.AssetUseCase.GetAssetByID(ctx, ticket.ReferenceID)
		if err != nil {
			commonlogger.Warnf("Error in getting asset by asset id from asset service", err)
			return
		}

		if asset.ReferenceNumber == "" {
			assetName = fmt.Sprintf("%s - %s", asset.Name, asset.SerialNumber)
		} else {
			assetName = fmt.Sprintf("%s - %s", asset.Name, asset.ReferenceNumber)
		}
	}

	user := &userIdentityModel.User{}
	if ticket.AssignedToUserID.String != "" {
		var err error
		user, err = uc.UserRepository.GetUser(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
			Where: userIdentityModel.UserWhere{
				ID: ticket.AssignedToUserID.String,
			},
		})
		if err != nil {
			commonlogger.Warnf("error get user on notify update ticket status", err)
			return
		}
	}

	client, err := uc.UserRepository.GetClient(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
		Where: userIdentityModel.ClientWhere{
			ID: ticket.ClientID,
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get client for email %v", err)
		return
	}

	href := uc.notifUseCase.GenerateTargetURL(ctx, client.ClientAlias, notifConstants.DESTINATION_TYPE_WORK_ORDER, ticket.ID)

	templateBod := tmplhelpers.CreateTicketBody{
		Subject:        ticket.Subject,
		AssetName:      assetName,
		UserAssigned:   "-",
		RedirectWOLink: template.URL(href),
		SeverityLevel:  "-",
		TicketDesc:     "-",
	}
	if constants.MapTicketSeverityLabel[ticket.SeverityLevelCode] != "" {
		templateBod.SeverityLevel = constants.MapTicketSeverityLabel[ticket.SeverityLevelCode]
	}
	if ticket.Description != "" {
		templateBod.TicketDesc = ticket.Description
	}
	if user.GetName() != "" {
		templateBod.UserAssigned = user.GetName()
	}

	assetAssignment, err := uc.assetAssignmentRepo.GetAssetAssignment(ctx, uc.DB.DB(), assetServiceModels.AssetAssignmentCondition{
		Where: assetServiceModels.AssetAssignmentWhere{
			AssetID:  ticket.ReferenceID,
			Assigned: true,
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get asset assignment for id %v", err)
		return
	}

	notifItem := notificationDtos.CreateNotificationItem{
		SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_TICKET_ASSIGNMENT,
		SourceReferenceID: ticket.ID,
		TargetReferenceID: ticket.ReferenceID,
		TargetURL:         href,
		MessageHeader:     templateBod.ConstructTitleEmail(),
		MessageBody:       templateBod.ConstructBodyEmail(),
		MessageFirebase: notificationDtos.MessageFirebase{
			Title: templateBod.ConstructTitlePushNotif(),
			Body:  templateBod.ConstructBodyPushNotif(),
		},
		ClientID:        ticket.ClientID,
		TypeCode:        "",
		ContentTypeCode: "",
		ReferenceCode:   notifConstants.NOTIF_REF_WORK_ORDER,
		ReferenceValue:  ticket.ID,
		UserID:          assetAssignment.UserID,
	}

	itemNotifications := []notificationDtos.CreateNotificationItem{
		notifItem,
	}

	uc.notifUseCase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
		Items:           itemNotifications,
		SendToEmail:     true,
		SendToPushNotif: true,
	})
}

func (uc *TicketUseCase) UpdateTicket(ctx context.Context, updateDTO *dtos.TicketResponse) (*models.Ticket, error) {
	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	existingTicket, err := uc.TicketRepository.GetTicket(ctx, tx.DB(), models.TicketCondition{
		Where: models.TicketWhere{
			ID:       updateDTO.TicketUUID,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	existingTicket.Subject = updateDTO.Subject
	existingTicket.Description = updateDTO.Description
	existingTicket.TicketCategoryCode = updateDTO.TicketCategoryCode
	existingTicket.TicketReferenceCode = updateDTO.TicketReferenceCode
	existingTicket.ReferenceID = updateDTO.ReferenceID
	existingTicket.SeverityLevelCode = updateDTO.SeverityLevelCode
	existingTicket.RequesterUserID = updateDTO.RequesterUserID
	existingTicket.AssignedToUserID = updateDTO.AssignedToUserID
	existingTicket.StatusCode = updateDTO.StatusCode
	existingTicket.ClientID = updateDTO.ClientID
	existingTicket.Resolution = updateDTO.Resolution
	existingTicket.Cost = updateDTO.Cost
	existingTicket.DueDatetime = parseDate(updateDTO.DueDatetime)

	err = uc.TicketRepository.UpdateTicket(ctx, tx.DB(), existingTicket)
	if err != nil {
		return nil, err
	}

	_, err = uc.attachmentUseCase.UpdateAttachmentPhotos(ctx, storageDtos.UpsertAttachmentReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_TICKET,
		SourceReferenceID: existingTicket.ID,
		ClientID:          claim.GetLoggedInClientID(),
		Photos:            updateDTO.Photos,
	})

	if err != nil {
		return nil, err
	}

	ticketContacts := make([]models.TicketContact, 0, len(updateDTO.Contacts))
	for _, contact := range updateDTO.Contacts {
		ticketContacts = append(ticketContacts, models.TicketContact{
			Name:                contact.Name,
			PhoneNumber:         contact.PhoneNumber,
			Email:               contact.Email,
			Role:                contact.Role,
			TicketID:            updateDTO.TicketUUID,
			TypeCode:            contact.TypeCode,
			ReferenceSourceID:   contact.ReferenceSourceID,
			ReferenceSourceCode: contact.ReferenceSourceCode,
		})
	}

	err = uc.TicketRepository.UpsertTicketContacts(ctx, tx.DB(), ticketContacts)
	if err != nil {
		return nil, err
	}

	ticketNote := models.TicketNote{
		TicketID: existingTicket.ID,
		UserID:   claim.UserID,
		Title:    fmt.Sprintf("%s has edited the work order.", claim.GetName()),
		Notes:    "",
		ClientID: claim.GetLoggedInClientID(),
	}

	err = uc.TicketRepository.CreateNote(ctx, tx.DB(), &ticketNote)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	go uc.notifyAfterUpdateTicket(contexthelpers.WithoutCancel(ctx), existingTicket)
	return existingTicket, nil
}

func (uc *TicketUseCase) UpdateSchedule(ctx context.Context, id string, req dtos.UpdateScheduleReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}
	_, err = uc.TicketRepository.GetTicket(ctx, uc.DB.DB(), models.TicketCondition{
		Where: models.TicketWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}
	var updatedSchedule *time.Time
	log.Println("default ", updatedSchedule)
	if req.ScheduleDatetime != "" {
		updatedSchedule = parseDate(req.ScheduleDatetime)
	}
	err = uc.TicketRepository.UpdateTicketSchedule(ctx, uc.DB.DB(), id, updatedSchedule)
	if err != nil {
		return nil, err
	}
	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *TicketUseCase) getRedirectLink(ticket *models.Ticket) string {
	apiURL := internalConstants.API_STAGING_URL
	if os.Getenv(internalConstants.ENV_APP_ENV) == "production" {
		apiURL = internalConstants.API_URL
	}
	return fmt.Sprintf("http://%s/v1/redirects?type=%s&ref=%s&client_id=%s", apiURL, notifConstants.REDIRECT_TYPE_WORK_ORDER, ticket.ID, ticket.ClientID)
}

func (uc *TicketUseCase) notifyAfterUpdateTicket(
	ctx context.Context,
	ticket *models.Ticket,
) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return
	}

	// Get Ticket Subject
	ticket, err = uc.TicketRepository.GetTicket(ctx, uc.DB.DB(), models.TicketCondition{
		Where: models.TicketWhere{
			ID: ticket.ID,
		},
		Preload: models.TicketPreload{
			TicketCategory: true,
		},
	})
	if err != nil {
		commonlogger.Warnf("Error in getting ticket preload", err)
		return
	}

	assetName := ""
	platNo := ""
	asset := &assetServiceModels.Asset{}
	if ticket.ReferenceID != constants.GENERAL_TICKET_REFERENCE_ID {
		if ticket.TicketReferenceCode == constants.TICKET_ASSET_VEHICLE_REF {
			asset, err = uc.AssetUseCase.GetAssetByID(ctx, ticket.ReferenceID)
			if err != nil {
				commonlogger.Warnf("Error in getting asset by asset id from asset service", err)
				return
			}

			if asset.ReferenceNumber == "" {
				assetName = fmt.Sprintf("%s - %s", asset.Name, asset.SerialNumber)
				platNo = asset.SerialNumber
			} else {
				assetName = fmt.Sprintf("%s - %s", asset.Name, asset.ReferenceNumber)
				platNo = asset.ReferenceNumber
			}
		}
	}
	if ticket.AssignedToUserID.String == "" {
		return
	}

	user, err := uc.UserRepository.GetUser(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
		Where: userIdentityModel.UserWhere{
			ID: ticket.AssignedToUserID.String,
		},
	})
	if err != nil {
		commonlogger.Warnf("error get user on notify update ticket status", err)
		return
	}

	client, err := uc.UserRepository.GetClient(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
		Where: userIdentityModel.ClientWhere{
			ID: ticket.ClientID,
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get client for email %v", err)
		return
	}

	href := uc.notifUseCase.GenerateTargetURL(ctx, client.ClientAlias, notifConstants.DESTINATION_TYPE_WORK_ORDER, ticket.ID)

	templateBod := tmplhelpers.UpdateTicketBody{
		Subject:        ticket.TicketCategory.Label,
		AssetName:      assetName,
		UserAssigned:   user.GetName(),
		RedirectWOLink: template.URL(href),
		TicketDesc:     "-",
		SeverityLevel:  "-",
		DueDate:        "-",
		PlatNo:         "-",
		CustomerName:   "Customer Name",
		IsFromWorkshop: ticket.TicketCategoryCode == constants.TICKET_CATEGORY_CODE_WORKSHOP_SERVICE,
	}
	if ticket.Description != "" {
		templateBod.TicketDesc = ticket.Description
	}
	if constants.MapTicketSeverityLabel[ticket.SeverityLevelCode] != "" {
		templateBod.SeverityLevel = constants.MapTicketSeverityLabel[ticket.SeverityLevelCode]
	}
	if ticket.DueDatetime != nil {
		loc, _ := time.LoadLocation("Asia/Jakarta")
		templateBod.DueDate = ticket.DueDatetime.In(loc).Format(timehelpers.RFC1123Notif)
	}
	if asset.PartnerOwnerName != "" {
		templateBod.CustomerName = asset.PartnerOwnerName
	}
	if platNo != "" {
		templateBod.PlatNo = platNo
	}

	notifItem := notificationDtos.CreateNotificationItem{
		SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_TICKET_UPDATE,
		SourceReferenceID: ticket.ID,
		TargetReferenceID: ticket.ReferenceID,
		TargetURL:         href,
		MessageHeader:     templateBod.ConstructSubjectEmail(),
		MessageBody:       templateBod.ConstructBodyEmail(),
		MessageFirebase: notificationDtos.MessageFirebase{
			Title: templateBod.ConstructSubjectSenNotif(),
			Body:  templateBod.ConstructBodySenNotif(),
		},
		ClientID:        ticket.ClientID,
		TypeCode:        "",
		ContentTypeCode: "",
		ReferenceCode:   notifConstants.NOTIF_REF_WORK_ORDER,
		ReferenceValue:  ticket.ID,
	}

	// if editor is assigned user, then send to requester
	isLoginRequester := claim.UserID == ticket.RequesterUserID
	if isLoginRequester {
		notifItem.UserID = ticket.AssignedToUserID.String
	} else {
		notifItem.UserID = ticket.RequesterUserID
	}

	_ = uc.notifUseCase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
		Items:           []notificationDtos.CreateNotificationItem{notifItem},
		SendToEmail:     true,
		SendToPushNotif: true,
	})
}

func (uc *TicketUseCase) GenerateTicketNonViewAllCondition(ctx context.Context, claim authhelpers.JwtTokenClaims) (*models.NonTicketAdminCondition, error) {
	assets, err := uc.assetAssignmentRepo.GetAssetAssignments(ctx, uc.DB.DB(),
		assetServiceModels.AssetAssignmentCondition{
			Where: assetServiceModels.AssetAssignmentWhere{
				UserID:   claim.UserID,
				Assigned: true,
			},
			Columns: []string{"asset_id"},
		})
	if err != nil {
		return nil, err
	}

	assetIDs := []string{}
	for i := range assets {
		assetIDs = append(assetIDs, assets[i].AssetID)
	}

	userClient, err := uc.UserRepository.GetUserClient(ctx, uc.DB.DB(),
		userIdentityModel.UserClientCondition{
			Where: userIdentityModel.UserClientWhere{
				UserID:   claim.UserID,
				ClientID: claim.GetLoggedInClientID(),
			},
		})
	if err != nil {
		return nil, err
	}

	return &models.NonTicketAdminCondition{
		UserID:       claim.UserID,
		ReferenceIDs: assetIDs,
		DepartmentID: null.NewString(userClient.DepartmentID.String, userClient.DepartmentID.String != ""),
	}, nil
}

func (uc *TicketUseCase) GetTicketDetail(ctx context.Context, id string) (*dtos.TicketDetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	whereCondition := models.TicketWhere{
		ClientID: claim.GetLoggedInClientID(),
		ID:       id,
	}

	ticket, err := uc.TicketRepository.GetTicket(ctx, uc.DB.DB(), models.TicketCondition{
		Where: whereCondition,
		Preload: models.TicketPreload{
			TicketCategory: true,
		},
	})
	if err != nil {
		return nil, err
	}

	workOrderGroup, _ := claim.GetPermissionCategory(constants.TICKET_PERMISSION_WORK_ORDER)
	isWOViewAll := uc.isWOViewAll(workOrderGroup)

	if !isWOViewAll {
		nonViewAllCondition, err := uc.GenerateTicketNonViewAllCondition(ctx, claim)
		if err != nil {
			return nil, err
		}

		whereCondition.NonTicketAdminCondition = nonViewAllCondition

		_, err = uc.TicketRepository.GetTicket(ctx, uc.DB.DB(), models.TicketCondition{
			Where: whereCondition,
			Preload: models.TicketPreload{
				TicketCategory: true,
			},
		})
		if err != nil && !errorhandler.IsErrNotFound(err) {
			return nil, err
		} else if errorhandler.IsErrNotFound(err) {
			return nil, errorhandler.ErrBadRequest(errorhandler.ErrNoPermission)
		}
	}

	departmentName := ""
	if ticket.DepartmentID != "" {
		department, err := uc.departmentRepo.GetDepartment(ctx, uc.DB.DB(), userIdentityModel.DepartmentCondition{
			Where: userIdentityModel.DepartmentWhere{
				ID:       ticket.DepartmentID,
				ClientID: ticket.ClientID,
			},
		})
		if err == nil {
			departmentName = department.Name
		}
	}

	var userIds []string
	if ticket.RequesterUserID != "" {
		userIds = append(userIds, ticket.RequesterUserID)
	}

	if ticket.AssignedToUserID.String != "" {
		userIds = append(userIds, ticket.AssignedToUserID.String)
	}

	if ticket.ResolutionBy != "" {
		userIds = append(userIds, ticket.ResolutionBy)
	}

	usersMapById := map[string]userIdentityModel.User{}

	err = uc.UserRepository.GetUsersInMapByIds(ctx, uc.DB.DB(), &usersMapById, userIds)
	if err != nil {
		commonlogger.Errorf("Error in getting users by user ids from identity service", err)
		return nil, err
	}

	asset := assetServiceModels.Asset{}

	tempAsset, err := uc.AssetRepository.GetAsset(ctx, uc.DB.DB(), assetServiceModels.AssetCondition{
		Where: assetServiceModels.AssetWhere{
			ID: ticket.ReferenceID,
		},
		Preload: assetServiceModels.AssetPreload{
			AssetTyre:              true,
			Brand:                  true,
			AssetCategory:          true,
			SubCategory:            true,
			CustomAssetCategory:    true,
			CustomAssetSubCategory: true,
			Location:               true,
		},
	})
	if err == nil {
		asset = *tempAsset
	}

	if ticket.AssetDataInformation.Status == pgtype.Undefined || ticket.AssetDataInformation.Status == pgtype.Null {
		dataInformation, err := uc.AssetRepository.GetAssetDataInformation(ctx, uc.DB.DB(), ticket.ReferenceID)
		if err != nil {
			return nil, err
		}

		ticket.AssetDataInformation = pgtype.JSONB{Bytes: dataInformation, Status: pgtype.Present}
	}

	response := dtos.BuildTicketDetailResponse(ticket, usersMapById, asset, departmentName)

	if response.AssetDataInformation.Photo != "" {
		response.AssetDataInformation.Photo, err = uc.storageRepository.GetFileSignedURL(ctx, response.AssetDataInformation.Photo, time.Now().Add(24*time.Hour))
		if err != nil {
			commonlogger.Errorf("error get photo from gcp, err %v", err)
		}
	}

	return &response, nil
}

func (uc *TicketUseCase) CloseTicket(ctx context.Context, id, notes string) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}

	ticket, err := uc.TicketRepository.GetTicketByID(ctx, uc.DB.DB(), id)
	if err != nil {
		return err
	}

	if ticket.StatusCode == constants.TICKET_STATUS_CODE_CLOSED {
		return nil
	}

	if ticket.ClientID != claim.GetLoggedInClientID() {
		return errorhandler.ErrNotEligible
	}

	if ticket.StartTime != nil && ticket.StartTime.Valid {
		ticket.RunningTime += int(time.Since(ticket.StartTime.Time).Seconds())
	}

	err = uc.createTicketNote(ctx, id, ticket.StatusCode, constants.TICKET_STATUS_CODE_CLOSED, notes)
	if err != nil {
		return err
	}

	updateTicket := &models.Ticket{
		StatusCode:  constants.TICKET_STATUS_CODE_CLOSED,
		RunningTime: ticket.RunningTime,
		StartTime:   &sqlhelpers.SqlNullTime{},
	}
	updateTicket.ID = id
	err = uc.TicketRepository.UpdateTicket(ctx, uc.DB.WithCtx(ctx).DB(), updateTicket)
	if err != nil {
		return err
	}

	journalReferences := []financeDtos.JournalReference{
		{
			ReferenceID: ticket.ReferenceID,
			// Currentlny always assume ticket came from asset
			SourceCode: financeConstants.JOURNAL_SOURCE_ASSET_CODE,
		},
		{
			ReferenceID: ticket.ID,
			SourceCode:  financeConstants.JOURNAL_SOURCE_WORK_ORDER_CODE,
		},
	}

	tread, err := uc.assetTyreRepo.GetAssetTyreTread(ctx, uc.DB.DB(), assetServiceModels.AssetTyreTreadCondition{
		Where: assetServiceModels.AssetTyreTreadWhere{
			AssetID:   ticket.ReferenceID,
			IsLastest: true,
		},
	})
	if err == nil {
		journalReferences = append(journalReferences, financeDtos.JournalReference{
			SourceCode:  financeConstants.JOURNAL_SOURCE_RETREAD_TYRE_CODE,
			ReferenceID: tread.ID,
		})
	}

	err = uc.financeUseCase.CreateJournal(ctx, financeDtos.CreateJournalReq{
		AccountTransactionType: financeConstants.ACCOUNT_TRANSACTION_TYPE_TICKET_RESOLUTION_COST,
		Date:                   updateTicket.UpdatedAt,
		References:             journalReferences,
		Notes:                  "Ticket Resolved",
		Amount:                 ticket.Cost.Int64,
		ClientID:               claim.GetLoggedInClientID(),
		UserID:                 claim.UserID,
	})
	if err != nil {
		return err
	}

	go uc.notifyAfterUpdateTicketStatus(contexthelpers.WithoutCancel(ctx), updateTicket, notes, constants.TICKET_STATUS_CODE_CLOSED)

	return nil
}

func (uc *TicketUseCase) CancelTicket(ctx context.Context, id string, notes string) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}

	ticket, err := uc.TicketRepository.GetTicket(ctx, uc.DB.DB(), models.TicketCondition{
		Where: models.TicketWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return err
	}

	if ticket.StatusCode == constants.TICKET_STATUS_CODE_CANCELED {
		return nil
	}

	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return err
	}

	defer tX.Rollback()

	updateTicket := &models.Ticket{StatusCode: constants.TICKET_STATUS_CODE_CANCELED}
	updateTicket.ID = id
	err = uc.TicketRepository.UpdateTicket(ctx, tX.DB(), updateTicket)
	if err != nil {
		return err
	}

	err = uc.assetInspectionRepo.DoneInspectionByReferenceID(ctx, tX.DB(), id)
	if err != nil {
		return err
	}

	err = uc.createTicketNote(ctx, id, ticket.StatusCode, constants.TICKET_STATUS_CODE_CANCELED, notes)
	if err != nil {
		return err
	}

	err = tX.Commit()
	if err != nil {
		return err
	}

	go uc.notifyAfterUpdateTicketStatus(contexthelpers.WithoutCancel(ctx), updateTicket, "-", constants.TICKET_STATUS_CODE_CANCELED)
	return nil
}

func (uc *TicketUseCase) AssignedToUser(ctx context.Context, id string, req dtos.AssignTicketToUserReq) error {
	if req.DepartmentID == "" {
		return errorhandler.ErrBadRequest("mandatory request field is empty")
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}

	ticket, err := uc.TicketRepository.GetTicketByID(ctx, uc.DB.DB(), id)
	if err != nil {
		return err
	}

	if ticket.StatusCode == constants.TICKET_STATUS_CODE_CLOSED ||
		ticket.StatusCode == constants.TICKET_STATUS_CODE_CANCELED {
		return errorhandler.ErrNotAllowed("ticket was canceled or closed")
	}

	if ticket.ClientID != claim.GetLoggedInClientID() {
		return errorhandler.ErrNotEligible
	}

	userName := ""
	departmentAssigneeNew := ""
	if req.UserID.String != "" {
		userData, err := uc.UserRepository.GetUser(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
			Where: userIdentityModel.UserWhere{
				ID:       req.UserID.String,
				ClientID: claim.GetLoggedInClientID(),
			},
			Preload: userIdentityModel.UserPreload{
				CurrentUserClientGroup: true,
			},
		})
		if err != nil {
			return err
		}

		userName = userData.GetName()
		departmentAssigneeNew = userData.CurrentUserClient.Department.Name
	}

	department, err := uc.departmentRepo.GetDepartment(ctx, uc.DB.DB(), userIdentityModel.DepartmentCondition{
		Where: userIdentityModel.DepartmentWhere{
			ID:       req.DepartmentID,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return err
	}

	// Maybe need to make sure user related with client

	updateTicket := &models.Ticket{
		StatusCode:       constants.TICKET_STATUS_CODE_ASSIGNED,
		AssignedToUserID: req.UserID,
		DepartmentID:     req.DepartmentID,
	}
	if req.UserID.String == "" {
		updateTicket.StatusCode = constants.TICKET_STATUS_CODE_OPEN
	}

	updateTicket.ID = id
	err = uc.TicketRepository.UpdateTicket(ctx, uc.DB.WithCtx(ctx).DB(), updateTicket)
	if err != nil {
		return err
	}

	noteTitle := fmt.Sprintf("%s assigns %s", claim.GetName(), department.Name)
	if userName != "" {
		noteTitle = fmt.Sprintf("%s assigns %s from %s", claim.GetName(), userName, departmentAssigneeNew)
	}
	// if from status resolve (reopen state)
	if ticket.StatusCode == constants.TICKET_STATUS_CODE_RESOLVED {
		noteTitle = fmt.Sprintf("%s changes status from Resolved to Assigned", claim.GetName())
	}

	ticketNote := models.TicketNote{
		TicketID: id,
		UserID:   claim.UserID,
		Title:    noteTitle,
		Notes:    req.Note,
		ClientID: claim.GetLoggedInClientID(),
	}

	err = uc.TicketRepository.CreateNote(ctx, uc.DB.DB(), &ticketNote)
	if err != nil {
		return err
	}

	scenario := 0
	if claim.UserID == ticket.RequesterUserID { // login as wo requester
		if req.UserID.String != ticket.RequesterUserID {
			scenario = 1
		}
	} else { // login as no requester (should be assignee)
		if req.UserID.String != claim.UserID {
			scenario = 1
		}
	}

	/*
		scenario before, above is simplified
	*/
	/*
		if claim.UserID == ticket.AssignedToUserID.String && ticket.AssignedToUserID.String != ticket.RequesterUserID {
			if req.UserID != ticket.AssignedToUserID && req.UserID.String != claim.UserID {
				scenario = 4
			}
		} else {
			// Requester login
			switch {
			case ticket.RequesterUserID == req.UserID.String:
				scenario = 0
			case ticket.AssignedToUserID.Valid:
				scenario = 1
			case ticket.AssignedToUserID.String == ticket.RequesterUserID && req.UserID != ticket.AssignedToUserID:
				scenario = 2
			case ticket.AssignedToUserID.Valid == false && req.UserID != ticket.AssignedToUserID && req.UserID.String != ticket.RequesterUserID:
				scenario = 3
			}
		}
	*/

	if scenario != 0 {
		go uc.notifyAfterAssignedToUser(contexthelpers.WithoutCancel(ctx), updateTicket, &ticketNote, userName, ticket, scenario)
	}

	return nil
}

func (uc *TicketUseCase) notifyAfterAssignedToUser(
	ctx context.Context,
	ticket *models.Ticket,
	note *models.TicketNote,
	userNameAssigned string,
	ticketPrev *models.Ticket,
	scenario int,
) {
	// Get Ticket Subject
	ticket, err := uc.TicketRepository.GetTicket(ctx, uc.DB.DB(), models.TicketCondition{
		Where: models.TicketWhere{
			ID: ticket.ID,
		},
		Preload: models.TicketPreload{
			TicketCategory: true,
		},
	})
	if err != nil {
		commonlogger.Warnf("Error in getting ticket preload", err)
		return
	}

	assetName := ""
	platNo := ""
	asset := &assetServiceModels.Asset{}
	if ticket.TicketReference.Label != constants.GENERAL_TICKET_REFERENCE_ID {
		if ticket.TicketReferenceCode == constants.TICKET_ASSET_VEHICLE_REF {
			asset, err = uc.AssetUseCase.GetAssetByID(ctx, ticket.ReferenceID)
			if err != nil {
				commonlogger.Warnf("Error in getting asset by asset id from asset service", err)
				return
			}

			if asset.ReferenceNumber == "" {
				assetName = fmt.Sprintf("%s - %s", asset.Name, asset.SerialNumber)
				platNo = asset.SerialNumber
			} else {
				assetName = fmt.Sprintf("%s - %s", asset.Name, asset.ReferenceNumber)
				platNo = asset.ReferenceNumber
			}
		}
	}

	client, err := uc.UserRepository.GetClient(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
		Where: userIdentityModel.ClientWhere{
			ID: ticket.ClientID,
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get client for email %v", err)
		return
	}

	user := userIdentityModel.User{}
	err = uc.UserRepository.GetUserById(ctx, uc.DB.DB(), &user, ticket.AssignedToUserID.String)
	if err != nil {
		commonlogger.Warnf("failed to get user for assign %v", err)
		return
	}

	href := uc.notifUseCase.GenerateTargetURL(ctx, client.ClientAlias, notifConstants.DESTINATION_TYPE_WORK_ORDER, ticket.ID)
	loc, _ := time.LoadLocation("Asia/Jakarta")
	templateBod := tmplhelpers.UpdateTicketAsigneeBod{
		Subject:          ticket.TicketCategory.Label,
		AssetName:        assetName,
		UserAssigned:     "-",
		Note:             note.Notes,
		RedirectWOLink:   tmplhelpers.ParseURLTemplate(href),
		Scenario:         scenario,
		CustomerName:     "Customer Name",
		IsFromWorkshop:   ticket.TicketCategoryCode == constants.TICKET_CATEGORY_CODE_WORKSHOP_SERVICE,
		PlatNo:           "-",
		SeverityLevel:    "-",
		TicketDesc:       "-",
		DueDate:          "-",
		ScheduleDate:     "-",
		PreviousAssignee: "-",
	}
	if user.GetName() != "" {
		templateBod.UserAssigned = user.GetName()
	}
	if ticket.Description != "" {
		templateBod.TicketDesc = ticketPrev.Description
	}

	if userNameAssigned != "" {
		templateBod.UserAssigned = userNameAssigned
	}
	if constants.MapTicketSeverityLabel[ticket.SeverityLevelCode] != "" {
		templateBod.SeverityLevel = constants.MapTicketSeverityLabel[ticket.SeverityLevelCode]
	}
	if ticketPrev.DueDatetime != nil {
		templateBod.DueDate = ticketPrev.DueDatetime.In(loc).Format(timehelpers.RFC1123Notif)
	}
	if ticketPrev.ScheduleDatetime != nil {
		templateBod.ScheduleDate = ticketPrev.ScheduleDatetime.In(loc).Format(timehelpers.RFC1123Notif)
	}
	if ticketPrev.AssignedToUserID.Valid {
		userPrev := &userIdentityModel.User{}
		uc.UserRepository.GetUserById(ctx, uc.DB.DB(), userPrev, ticketPrev.AssignedToUserID.String)
		templateBod.PreviousAssignee = userPrev.GetName()
	}
	if asset.PartnerOwnerName != "" {
		templateBod.CustomerName = asset.PartnerOwnerName
	}
	if platNo != "" {
		templateBod.PlatNo = platNo
	}

	notifItem := notificationDtos.CreateNotificationItem{
		UserID:            ticket.AssignedToUserID.String,
		SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_TICKET_UPDATE,
		SourceReferenceID: ticket.ID,
		TargetReferenceID: ticket.ReferenceID,
		TargetURL:         href,
		MessageHeader:     templateBod.ConstructSubjectEmail(),
		MessageBody:       templateBod.ConstructBodyEmail(),
		MessageFirebase: notificationDtos.MessageFirebase{
			Title: templateBod.ConstrucPushNotifSubject(),
			Body:  templateBod.ConstrucPushNotifBody(),
		},
		ClientID:        ticket.ClientID,
		TypeCode:        "",
		ContentTypeCode: "",
		ReferenceCode:   notifConstants.NOTIF_REF_WORK_ORDER,
		ReferenceValue:  ticket.ID,
	}

	notifications := []notificationDtos.CreateNotificationItem{}
	if scenario == 1 || scenario == 2 {
		notifications = append(notifications, notifItem)
	} else if scenario == 3 { // assign to the work
		notifications = append(notifications, notifItem)
		notifItem.UserID = ticketPrev.AssignedToUserID.String // previous recipient
		notifications = append(notifications, notifItem)
	} else if scenario == 4 {
		notifications = append(notifications, notifItem)
		notifItem.UserID = ticketPrev.RequesterUserID
		notifications = append(notifications, notifItem)
	}

	_ = uc.notifUseCase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
		Items:           notifications,
		SendToEmail:     true,
		SendToPushNotif: true,
	})
}

func (uc *TicketUseCase) AddTicketResolution(ctx context.Context, id string, req dtos.TicketResolutionDto) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}

	ticket, err := uc.TicketRepository.GetTicketByID(ctx, uc.DB.DB(), id)
	if err != nil {
		commonlogger.Warnf("Error getting ticket", err)
		return err
	}

	if ticket.StatusCode == constants.TICKET_STATUS_CODE_CLOSED {
		return errorhandler.ErrNotAllowed("ticket was aleady closed")
	}

	if ticket.StartTime != nil && ticket.StartTime.Valid {
		ticket.RunningTime += int(time.Since(ticket.StartTime.Time).Seconds())
	}

	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return err
	}

	defer tX.Rollback()

	updateTicket := &models.Ticket{
		StatusCode:     constants.TICKET_STATUS_CODE_RESOLVED,
		Resolution:     req.Resolution,
		Cost:           req.Cost,
		RunningTime:    ticket.RunningTime,
		StartTime:      &sqlhelpers.SqlNullTime{},
		ResolutionBy:   claim.UserID,
		ResolutionNote: req.Note,
	}
	updateTicket.ID = id

	err = uc.TicketRepository.UpdateTicket(ctx, tX.DB(), updateTicket)
	if err != nil {
		return err
	}

	err = uc.createTicketNote(ctx, id, ticket.StatusCode, constants.TICKET_STATUS_CODE_RESOLVED, req.Note)
	if err != nil {
		return err
	}

	err = uc.assetInspectionRepo.DoneInspectionByReferenceID(ctx, tX.DB(), id)
	if err != nil {
		return err
	}

	err = tX.Commit()
	if err != nil {
		return err
	}

	go uc.notifyAfterUpdateTicketStatus(contexthelpers.WithoutCancel(ctx), updateTicket, req.Resolution, constants.TICKET_STATUS_CODE_RESOLVED)

	return nil
}

func (uc *TicketUseCase) notifyAfterAddTicketResolution(
	ctx context.Context,
	ticket *models.Ticket,
	note string,
) {
	assetName := ""
	if ticket.ReferenceID != constants.GENERAL_TICKET_REFERENCE_ID {
		if ticket.TicketReferenceCode == constants.TICKET_ASSET_VEHICLE_REF {
			asset, err := uc.AssetUseCase.GetAssetByID(ctx, ticket.ReferenceID)
			if err != nil {
				commonlogger.Warnf("Error in getting asset by asset id from asset service", err)
				return
			}

			if asset.ReferenceNumber == "" {
				assetName = fmt.Sprintf("%s - %s", asset.Name, asset.SerialNumber)
			} else {
				assetName = fmt.Sprintf("%s - %s", asset.Name, asset.ReferenceNumber)
			}
		}
	}

	user, err := uc.UserRepository.GetUser(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
		Where: userIdentityModel.UserWhere{
			ID: ticket.AssignedToUserID.String,
		},
	})
	if err != nil {
		commonlogger.Warnf("error get user on notify update ticket status", err)
		return
	}

	statusLabel := constants.MapStatusCodeToLabel()[ticket.StatusCode]
	href := tmplhelpers.ParseURLTemplate(uc.getRedirectLink(ticket))

	templateBod := tmplhelpers.UpdateTicketStatusBody{
		Subject:        ticket.Subject,
		AssetName:      assetName,
		UserAssigned:   user.GetName(),
		SeverityLevel:  constants.MapTicketSeverityLabel[ticket.SeverityLevelCode],
		Note:           "-",
		Cost:           int(ticket.Cost.Int64),
		RedirectWOLink: href,
		Status:         statusLabel,
	}
	if note != "" {
		templateBod.Note = note
	}
	if ticket.DueDatetime != nil {
		loc, _ := time.LoadLocation("Asia/Jakarta")
		templateBod.DueDate = ticket.DueDatetime.In(loc).Format(timehelpers.RFC1123Notif)
	} else {
		templateBod.DueDate = "-"
	}

	notifItem := notificationDtos.CreateNotificationItem{
		UserID:            ticket.AssignedToUserID.String,
		SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_TICKET_UPDATE,
		SourceReferenceID: ticket.ID,
		TargetReferenceID: ticket.ReferenceID,
		TargetURL:         string(href),
		MessageHeader:     templateBod.TitleEmail,
		MessageBody:       templateBod.BodyEmail,
		MessageFirebase: notificationDtos.MessageFirebase{
			Title: templateBod.TitleFirebase,
			Body:  templateBod.BodyFirebase,
		},
		ClientID:        ticket.ClientID,
		TypeCode:        "",
		ContentTypeCode: "",
		ReferenceCode:   notifConstants.NOTIF_REF_WORK_ORDER,
		ReferenceValue:  ticket.ID,
	}

	_ = uc.notifUseCase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
		Items:           []notificationDtos.CreateNotificationItem{notifItem},
		SendToEmail:     true,
		SendToPushNotif: true,
	})
}

func (uc *TicketUseCase) UpdateTicketStatus(ctx context.Context, id, status string, note string) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}

	ticket, err := uc.TicketRepository.GetTicketByID(ctx, uc.DB.DB(), id)
	if err != nil {
		return err
	}

	if ticket.StatusCode == status {
		return nil
	}

	if ticket.StatusCode == constants.TICKET_STATUS_CODE_CLOSED || ticket.StatusCode == constants.TICKET_STATUS_CODE_CANCELED {
		return errorhandler.ErrNotAllowed("ticket was canceled or closed")
	}

	if ticket.ClientID != claim.GetLoggedInClientID() {
		return errorhandler.ErrNotEligible
	}

	updateTicket := &models.Ticket{
		StatusCode: status,
	}
	updateTicket.ID = id

	if constants.TicketStatusStopStates(status) && ticket.StartTime != nil && ticket.StartTime.Valid {
		updateTicket.RunningTime = ticket.RunningTime + int(time.Since(ticket.StartTime.Time).Seconds())
		updateTicket.StartTime = &sqlhelpers.SqlNullTime{}
	}

	if constants.TicketStatusStartStates(status) &&
		(ticket.StartTime == nil || !ticket.StartTime.Valid) {
		startTime := sqlhelpers.NewSqlNullTime(time.Now())
		updateTicket.StartTime = &startTime
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return err
	}

	err = uc.TicketRepository.UpdateTicket(ctx, tx.DB(), updateTicket)
	if err != nil {
		return err
	}

	err = uc.createTicketNote(ctx, id, ticket.StatusCode, status, note)
	if err != nil {
		return err
	}

	err = tx.Commit()
	if err != nil {
		return err
	}

	if ticket.TicketCategoryCode != constants.TICKET_CATEGORY_CODE_WORKSHOP_SERVICE {
		go uc.notifyAfterUpdateTicketStatus(contexthelpers.WithoutCancel(ctx), updateTicket, note, status)
	}

	return nil
}

func (uc *TicketUseCase) notifyAfterUpdateTicketStatus(
	ctx context.Context,
	ticket *models.Ticket,
	note string,
	statusParam string,
) {
	handledStatuses := map[string]bool{
		constants.TICKET_STATUS_CODE_CLOSED:      true,
		constants.TICKET_STATUS_CODE_RESOLVED:    true,
		constants.TICKET_STATUS_CODE_IN_PROGRESS: true,
		constants.TICKET_STATUS_CODE_ON_HOLD:     true,
	}
	if !handledStatuses[statusParam] {
		return
	}

	ticket, err := uc.TicketRepository.GetTicket(ctx, uc.DB.DB(), models.TicketCondition{
		Where: models.TicketWhere{
			ID: ticket.ID,
		},
		Preload: models.TicketPreload{
			TicketCategory: true,
		},
	})
	if err != nil {
		commonlogger.Warnf("Error in getting ticket preload", err)
		return
	}

	assetName := ""
	if ticket.ReferenceID != constants.GENERAL_TICKET_REFERENCE_ID {
		if ticket.TicketReferenceCode == constants.TICKET_ASSET_VEHICLE_REF {
			asset, err := uc.AssetUseCase.GetAssetByID(ctx, ticket.ReferenceID)
			if err != nil {
				commonlogger.Warnf("Error in getting asset by asset id from asset service", err)
				return
			}

			if asset.ReferenceNumber == "" {
				assetName = fmt.Sprintf("%s - %s", asset.Name, asset.SerialNumber)
			} else {
				assetName = fmt.Sprintf("%s - %s", asset.Name, asset.ReferenceNumber)
			}
		}
	}

	user, err := uc.UserRepository.GetUser(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
		Where: userIdentityModel.UserWhere{
			ID: ticket.AssignedToUserID.String,
		},
	})
	if err != nil {
		commonlogger.Warnf("error get user on notify update ticket status", err)
		return
	}

	client, err := uc.UserRepository.GetClient(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
		Where: userIdentityModel.ClientWhere{
			ID: ticket.ClientID,
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get client for email %v", err)
		return
	}

	statusLabel := constants.MapStatusCodeToLabel()[ticket.StatusCode]
	href := uc.notifUseCase.GenerateTargetURL(ctx, client.ClientAlias, notifConstants.DESTINATION_TYPE_WORK_ORDER, ticket.ID)

	templateBod := tmplhelpers.UpdateTicketStatusBody{
		Subject:        ticket.TicketCategory.Label,
		AssetName:      assetName,
		TicketDesc:     "-",
		UserAssigned:   user.GetName(),
		SeverityLevel:  "-",
		Note:           "-",
		DueDate:        "-",
		Status:         statusLabel,
		RedirectWOLink: template.URL(href),
	}
	if constants.MapTicketSeverityLabel[ticket.SeverityLevelCode] != "" {
		templateBod.SeverityLevel = constants.MapTicketSeverityLabel[ticket.SeverityLevelCode]
	}
	if note != "" {
		templateBod.Note = note
	}
	if ticket.Description != "" {
		templateBod.TicketDesc = ticket.Description
	}
	if ticket.DueDatetime != nil {
		loc, _ := time.LoadLocation("Asia/Jakarta")
		templateBod.DueDate = ticket.DueDatetime.In(loc).Format(timehelpers.RFC1123Notif)
	}
	if ticket.Cost.Int64 != 0 {
		templateBod.Cost = int(ticket.Cost.Int64)
	}

	notifItem := notificationDtos.CreateNotificationItem{
		UserID:            "",
		SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_TICKET_UPDATE,
		SourceReferenceID: ticket.ID,
		TargetReferenceID: ticket.ReferenceID,
		TargetURL:         href,
		MessageHeader:     templateBod.GenerateSubjectEmail(),
		MessageBody:       templateBod.GenerateBodyEmail(),
		MessageFirebase: notificationDtos.MessageFirebase{
			Title: templateBod.GenerateSubjectPushNotif(),
			Body:  templateBod.GenerateBodyPushNotif(),
		},
		ClientID:        ticket.ClientID,
		TypeCode:        "",
		ContentTypeCode: "",
		ReferenceCode:   notifConstants.NOTIF_REF_WORK_ORDER,
		ReferenceValue:  ticket.ID,
	}

	// recipient
	switch statusParam {
	case constants.TICKET_STATUS_CODE_IN_PROGRESS, constants.TICKET_STATUS_CODE_RESOLVED, constants.TICKET_STATUS_CODE_ON_HOLD:
		notifItem.UserID = ticket.RequesterUserID
	case constants.TICKET_STATUS_CODE_CLOSED:
		notifItem.UserID = ticket.AssignedToUserID.String
	}

	_ = uc.notifUseCase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
		Items:           []notificationDtos.CreateNotificationItem{notifItem},
		SendToEmail:     true,
		SendToPushNotif: true,
	})
}

func (uc *TicketUseCase) AutoCreateTickets(ctx context.Context, createType string) error {
	switch createType {
	case "STNK_EXPIRY":
		return uc.AutoCreateTicketOnVrdAlmostExpiry(ctx)
	case "KIR_EXPIRY":
		return uc.CreateTicketOnInspectionBookAlmostExpiry(ctx)
	case "REMINDER_EXPIRY":
		return uc.AutoCreateTicketOnReminderExpiry(ctx)
	}

	return nil
}

func (uc *TicketUseCase) AutoCreateTicketOnVrdAlmostExpiry(ctx context.Context) error {
	assetVehicles, err := uc.assetVehicleRepo.GetAssetVehiclesWithExpiryVrdIn30Day(ctx, uc.DB.DB())
	if err != nil {
		return err
	}

	if len(assetVehicles) == 0 {
		return nil
	}

	assetIDs := []string{}
	for _, assetVehicle := range assetVehicles {
		assetIDs = append(assetIDs, assetVehicle.AssetID)
	}

	assetAssignments, err := uc.assetAssignmentRepo.GetAssetAssignments(ctx, uc.DB.DB(), assetServiceModels.AssetAssignmentCondition{
		Where: assetServiceModels.AssetAssignmentWhere{
			AssetIDs: assetIDs,
			Assigned: true,
		},
	})
	if err != nil {
		return err
	}

	mapClientIDToUserID := map[string]string{}
	for _, assetAssignment := range assetAssignments {
		mapClientIDToUserID[assetAssignment.ClientID] = assetAssignment.UserID
	}

	mapAssetIDToAssetName := map[string]string{}
	tickets := []models.Ticket{}
	for _, assetVehicle := range assetVehicles {
		mapAssetIDToAssetName[assetVehicle.AssetID] = assetVehicle.Asset.Name
		tickets = append(tickets, models.Ticket{
			Subject:             "Extend STNK for Vehicle " + assetVehicle.RegistrationNumber,
			TicketNumber:        "",
			Description:         "Please extend STNK before Expiry date " + assetVehicle.VrdExpiryDate.Format(time.DateOnly),
			TicketCategoryCode:  constants.TICKET_CATEGORY_CODE_OTHERS,
			TicketReferenceCode: constants.TICKET_ASSET_VEHICLE_REF,
			ReferenceID:         assetVehicle.AssetID,
			SeverityLevelCode:   constants.TICKET_SEVERITY_MEDIUM,
			RequesterUserID:     mapClientIDToUserID[assetVehicle.ClientID],
			AssignedToUserID:    null.StringFrom(mapClientIDToUserID[assetVehicle.ClientID]),
			StatusCode:          constants.TICKET_STATUS_CODE_ASSIGNED,
			Resolution:          "",
			ModelV2: commonmodel.ModelV2{
				ClientID: assetVehicle.ClientID,
			},
		})
	}

	err = uc.TicketRepository.CreateTickets(ctx, uc.DB.DB(), tickets)
	if err != nil {
		return err
	}

	go uc.notifyAfterAutoCreateTicketOnAlmostExpiryData(contexthelpers.WithoutCancel(ctx), tickets, mapAssetIDToAssetName)

	return nil
}

func (uc *TicketUseCase) CreateTicketOnInspectionBookAlmostExpiry(ctx context.Context) error {
	assetVehicles, err := uc.assetVehicleRepo.GetAssetVehiclesWithExpiryInspectionBookIn30Day(ctx, uc.DB.DB())
	if err != nil {
		return err
	}

	if len(assetVehicles) == 0 {
		return nil
	}

	mapAssetIDToAssetName := map[string]string{}
	tickets := []models.Ticket{}
	for _, assetVehicle := range assetVehicles {
		mapAssetIDToAssetName[assetVehicle.AssetID] = assetVehicle.Asset.Name
		tickets = append(tickets, models.Ticket{
			Subject:             "Extend KIR for Vehicle " + assetVehicle.RegistrationNumber,
			TicketNumber:        "",
			Description:         "Please extend KIR before Expiry date " + assetVehicle.InspectionBookExpiryDate.Format(time.DateOnly),
			TicketCategoryCode:  constants.TICKET_CATEGORY_CODE_OTHERS,
			TicketReferenceCode: constants.TICKET_ASSET_VEHICLE_REF,
			ReferenceID:         assetVehicle.AssetID,
			SeverityLevelCode:   constants.TICKET_SEVERITY_MEDIUM,
			RequesterUserID:     assetVehicle.InspectionBookNumberAssignTo,
			AssignedToUserID:    null.StringFrom(assetVehicle.InspectionBookNumberAssignTo),
			StatusCode:          constants.TICKET_STATUS_CODE_ASSIGNED,
			Resolution:          "",
			ModelV2: commonmodel.ModelV2{
				ClientID: assetVehicle.ClientID,
			},
		})
	}

	err = uc.TicketRepository.CreateTickets(ctx, uc.DB.DB(), tickets)
	if err != nil {
		return err
	}

	go uc.notifyAfterAutoCreateTicketOnAlmostExpiryData(contexthelpers.WithoutCancel(ctx), tickets, mapAssetIDToAssetName)

	return nil
}

func (uc *TicketUseCase) notifyAfterAutoCreateTicketOnAlmostExpiryData(
	ctx context.Context,
	tickets []models.Ticket,
	mapAssetIDToAssetName map[string]string,
) {

	userIDs := []string{}
	for _, ticket := range tickets {
		userIDs = append(userIDs, ticket.AssignedToUserID.String)
	}

	users, err := uc.UserRepository.GetUsersV2(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
		Where: userIdentityModel.UserWhere{
			IDs: userIDs,
		},
	})
	if err != nil {
		return
	}

	mapUser := map[string]userIdentityModel.User{}
	for _, user := range users {
		mapUser[user.ID] = user
	}

	notifItems := make([]notificationDtos.CreateNotificationItem, 0, len(tickets))
	for _, ticket := range tickets {
		_, ok := mapUser[ticket.AssignedToUserID.String]
		if !ok {
			continue
		}
		href := tmplhelpers.ParseURLTemplate(uc.getRedirectLink(&ticket))
		title, _ := tmplhelpers.ParseStringTemplate(
			"Ticket {{.Subject}} {{.AssetName}} is assigned",
			struct{ Subject, AssetName string }{
				Subject:   ticket.Subject,
				AssetName: mapAssetIDToAssetName[ticket.ReferenceID],
			})

		body, _ := tmplhelpers.ParseStringTemplate(
			`Work order ticket is assigned to you <br>
				<table>
				{{ range $key, $value := .Data }}
					<tr>
					<td>{{ $key }}</td>: <td>{{ $value }}</td>
					</tr>
				{{ end }}
				<br>
				<a href = "{{.RedirectLink}}"><button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">View Work Order Ticket</button></a><br>
				</table>
				<br>
				`,
			struct {
				Data         map[string]interface{}
				RedirectLink template.URL
			}{
				RedirectLink: template.URL(href),
				Data: map[string]interface{}{
					"Subject":     ticket.Subject,
					"Related":     mapAssetIDToAssetName[ticket.ReferenceID],
					"Assigned":    mapUser[ticket.AssignedToUserID.String].GetName(),
					"Priority":    constants.MapTicketSeverityLabel[ticket.SeverityLevelCode],
					"Description": ticket.Description,
				},
			})

		notifItems = append(notifItems, notificationDtos.CreateNotificationItem{
			UserID:            ticket.AssignedToUserID.String,
			SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_TICKET_UPDATE,
			SourceReferenceID: ticket.ID,
			TargetReferenceID: ticket.ReferenceID,
			TargetURL:         "",
			MessageHeader:     title,
			MessageBody:       body,
			ClientID:          ticket.ClientID,
			MessageFirebase: notificationDtos.MessageFirebase{
				Title: title,
			},
			TypeCode:        notifConstants.NOTIFICATION_TYPE_REMINDER_CODE,
			ContentTypeCode: "",
			ReferenceCode:   notifConstants.NOTIF_REF_WORK_ORDER,
			ReferenceValue:  ticket.ID,
		})
	}

	_ = uc.notifUseCase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
		Items:           notifItems,
		SendToEmail:     true,
		SendToPushNotif: true,
	})
}

func (uc *TicketUseCase) UpdateSeverityLevelCode(ctx context.Context, id string, req dtos.UpdateSeverityReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}
	err = uc.TicketRepository.UpdateTicketSeverityLevelCode(ctx, uc.DB.WithCtx(ctx).DB(), id, req.SeverityLevelCode)
	if err != nil {
		return nil, err
	}

	titleNote := fmt.Sprintf("%s sets priority to %s",
		claim.GetName(),
		constants.MapSeverityLevelCodeToLabel()[req.SeverityLevelCode],
	)
	ticketNote := models.TicketNote{
		TicketID: id,
		UserID:   claim.UserID,
		Title:    titleNote,
		Notes:    req.Note,
		ClientID: claim.GetLoggedInClientID(),
	}

	err = uc.TicketRepository.CreateNote(ctx, uc.DB.DB(), &ticketNote)
	if err != nil {
		return nil, err
	}
	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *TicketUseCase) upsertTicketContact(ctx context.Context, tx database.DBI, req dtos.UpsertTicketContact) error {
	ticketContact := &models.TicketContact{
		Name:                req.Name,
		PhoneNumber:         req.PhoneNumber,
		Email:               req.Email,
		Role:                req.Role,
		TicketID:            req.TicketID,
		TypeCode:            req.TypeCode,
		ReferenceSourceID:   req.ReferenceSourceID,
		ReferenceSourceCode: req.ReferenceSourceCode,
	}

	err := uc.TicketRepository.UpsertTicketContact(ctx, tx, ticketContact)
	if err != nil {
		return err
	}

	if req.TypeCode == constants.TICKET_CONTACT_TYPE_PIC_CHECK_IN {
		startTime := sqlhelpers.NewSqlNullTime(time.Now())
		now := time.Now()

		updateTicket := &models.Ticket{
			StatusCode:       constants.TICKET_STATUS_CODE_IN_QUEUE,
			StartTime:        &startTime,
			ScheduleDatetime: &now,
		}
		updateTicket.ID = req.TicketID
		err = uc.TicketRepository.UpdateTicket(ctx, tx, updateTicket)
		if err != nil {
			return err
		}
	}

	return nil
}

func (uc *TicketUseCase) UpsertTicketContact(ctx context.Context, req dtos.UpsertTicketContact) (*commonmodel.CreateResponse, error) {
	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer func() {
		tx.Rollback()
	}()

	err = uc.upsertTicketContact(ctx, tx.DB(), req)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        nil,
	}, nil
}

func (uc *TicketUseCase) GetTicketContactsByTicketID(ctx context.Context, ticketID string) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	ticketContacts, err := uc.TicketRepository.GetTicketContacts(ctx, uc.DB.DB(), models.TicketContactCondition{
		Where: models.TicketContactWhere{
			TicketID: ticketID,
			ClientID: claim.GetLoggedInClientID(),
		},
		Columns: []string{},
	})
	if err != nil {
		return nil, err
	}

	resp := make([]dtos.TicketContact, 0, len(ticketContacts))
	for i := range ticketContacts {
		resp = append(resp, dtos.TicketContact{
			ID:                  ticketContacts[i].ID,
			Name:                ticketContacts[i].Name,
			PhoneNumber:         ticketContacts[i].PhoneNumber,
			Email:               ticketContacts[i].Email,
			Role:                ticketContacts[i].Role,
			TicketID:            ticketContacts[i].TicketID,
			TypeCode:            ticketContacts[i].TypeCode,
			ReferenceSourceID:   ticketContacts[i].ReferenceSourceID,
			ReferenceSourceCode: ticketContacts[i].ReferenceSourceCode,
			CreatedAt:           ticketContacts[i].CreatedAt,
		})
	}
	return &commonmodel.ListResponse{
		TotalRecords: len(ticketContacts),
		PageSize:     len(ticketContacts),
		PageNo:       1,
		Data:         resp,
	}, nil
}

func (uc *TicketUseCase) createTicketNote(ctx context.Context, ticketID, ticketStatusBefore, ticketStatusAfter, notes string) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}

	titleNote := ""
	switch ticketStatusAfter {
	case constants.TICKET_STATUS_CODE_CLOSED:
		titleNote = fmt.Sprintf("%s closes the work order",
			claim.GetName(),
		)
	case constants.TICKET_STATUS_CODE_CANCELED:
		titleNote = fmt.Sprintf("%s cancels the work order and changes status from %s to Canceled.",
			claim.GetName(),
			constants.MapStatusCodeToLabel()[ticketStatusBefore],
		)
	default:
		titleNote = fmt.Sprintf("%s changes status from %s to %s",
			claim.GetName(),
			constants.MapStatusCodeToLabel()[ticketStatusBefore],
			constants.MapStatusCodeToLabel()[ticketStatusAfter],
		)
	}

	ticketNote := models.TicketNote{
		TicketID: ticketID,
		UserID:   claim.UserID,
		Title:    titleNote,
		Notes:    notes,
		ClientID: claim.GetLoggedInClientID(),
	}

	err = uc.TicketRepository.CreateNote(ctx, uc.DB.DB(), &ticketNote)
	if err != nil {
		return err
	}
	return nil
}
