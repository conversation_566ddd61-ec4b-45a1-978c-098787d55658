package dtos

type DisplayConfigItem struct {
	AnalyticCode string `json:"analytic_code"`
	IsActive     bool   `json:"is_active"`
	Sequence     int    `json:"sequence"`
}

type AnalyticDisplayConfigRequest struct {
	Configs []DisplayConfigItem `json:"configs"`
}

type GetAnalyticDisplayConfigRequest struct {
	AnalyticTypeCodes []string `form:"analytic_type_codes"`
}

type AnalyticDisplayConfigResponse struct {
	AnalyticCode     string `json:"analytic_code"`
	Label            string `json:"label"`
	Description      string `json:"description"`
	IsActive         bool   `json:"is_active"`
	Sequence         int    `json:"sequence"`
	AnalyticTypeCode string `json:"analytic_type_code"`
}
