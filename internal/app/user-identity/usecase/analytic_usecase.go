package usecase

import (
	"assetfindr/internal/app/user-identity/dtos"
	"assetfindr/internal/app/user-identity/models"
	"assetfindr/internal/app/user-identity/repository"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"context"
)

type AnalyticUseCase struct {
	DB                 database.DBUsecase
	AnalyticRepository repository.AnalyticRepository
}

func NewAnalyticUseCase(
	DB database.DBUsecase,
	analyticRepo repository.AnalyticRepository,
) *AnalyticUseCase {
	return &AnalyticUseCase{
		DB:                 DB,
		AnalyticRepository: analyticRepo,
	}
}

func (uc *AnalyticUseCase) GetAnalyticDisplayConfigs(ctx context.Context, req dtos.GetAnalyticDisplayConfigRequest) (commonmodel.ListResponse, error) {
	response := commonmodel.ListResponse{
		PageSize: 0,
		PageNo:   1,
	}
	var analyticDisplayConfigRespData []dtos.AnalyticDisplayConfigResponse

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return response, err
	}

	analyticDisplayConfigs, err := uc.AnalyticRepository.GetAnalyticDisplayConfigs(
		ctx,
		uc.DB.DB(),
		claim.GetLoggedInClientID(),
		claim.UserID,
		models.AnalyticDisplayConfigWhere{
			AnalyticTypeCodes: req.AnalyticTypeCodes,
		},
		models.AnalyticDisplayConfigPreload{
			Analytic: true,
		},
	)
	if err != nil {
		return response, err
	}

	var excludedAnalyticCode []string
	for _, analyticDisplayConfig := range analyticDisplayConfigs {
		excludedAnalyticCode = append(excludedAnalyticCode, analyticDisplayConfig.AnalyticCode)

		analyticDisplayConfigRespData = append(analyticDisplayConfigRespData, dtos.AnalyticDisplayConfigResponse{
			AnalyticCode: analyticDisplayConfig.AnalyticCode,
			Label:        analyticDisplayConfig.Analytic.Label,
			Description:  analyticDisplayConfig.Analytic.Description,
			IsActive:     analyticDisplayConfig.IsActive,
			Sequence:     analyticDisplayConfig.Sequence,
		})
	}

	analytics, err := uc.AnalyticRepository.GetAnalytics(
		ctx,
		uc.DB.DB(),
		models.AnalyticWhere{
			ExcludeAnalyticCode: excludedAnalyticCode,
			AnalyticTypeCodes:   req.AnalyticTypeCodes,
		},
	)
	println(len(analytics))

	if err != nil {
		return response, err
	}

	isDefaultActive := false
	if len(analyticDisplayConfigs) == 0 {
		isDefaultActive = true
	}
	for _, analytic := range analytics {
		analyticDisplayConfigRespData = append(analyticDisplayConfigRespData, dtos.AnalyticDisplayConfigResponse{
			AnalyticCode:     analytic.Code,
			Label:            analytic.Label,
			Description:      analytic.Description,
			IsActive:         isDefaultActive,
			Sequence:         analytic.Sequence,
			AnalyticTypeCode: analytic.AnalyticTypeCode,
		})
	}

	response.PageSize = len(analyticDisplayConfigRespData)
	response.TotalRecords = len(analyticDisplayConfigRespData)
	response.Data = analyticDisplayConfigRespData

	return response, nil
}

func (uc *AnalyticUseCase) UpsertAnalyticDisplayConfigs(ctx context.Context, req dtos.AnalyticDisplayConfigRequest) (*commonmodel.CreateResponse, error) {
	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	var displayConfigs []models.AnalyticDisplayConfig
	for _, config := range req.Configs {
		displayConfigs = append(displayConfigs, models.AnalyticDisplayConfig{
			AnalyticCode: config.AnalyticCode,
			Sequence:     config.Sequence,
			UserID:       claim.UserID,
			IsActive:     config.IsActive,
		})
	}

	err = uc.AnalyticRepository.UpsertAnalyticDisplayConfig(
		ctx,
		tx.DB(),
		&displayConfigs,
	)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Sucess",
		ReferenceID: claim.GetLoggedInClientID(),
		Data:        nil,
	}, nil
}
