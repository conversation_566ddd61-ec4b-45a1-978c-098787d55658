
BEGIN;

CREATE TABLE IF NOT EXISTS "ams_HAUL_STATUSES" (
    "code" VARCHAR(50) NOT NULL,
    "label" VARCHAR(50) NOT NULL,
    "description" VARCHAR(255) NOT NULL,
    "rank" SMALLINT,
    PRIMARY KEY ("code")
);


CREATE TABLE IF NOT EXISTS "ams_HAUL_ACTIVITIES" (
    "code" VARCHAR(50) NOT NULL,
    "label" VARCHAR(100) NOT NULL,
    "description" VARCHAR(255) NOT NULL,
    "haul_status_code" VARCHAR(50) NOT NULL,
    "rank" SMALLINT,
    "is_load" BOOLEAN,
    PRIMARY KEY ("code"),
    FOREIGN KEY ("haul_status_code") REFERENCES "ams_HAUL_STATUSES"("code")
);

CREATE TABLE IF NOT EXISTS "ams_HAUL_SUB_ACTIVITIES" (
    "code" VARCHAR(50) NOT NULL,
    "label" VARCHAR(100) NOT NULL,
    "description" VARCHAR(255) NOT NULL,
    "main_activity_code" VARCHAR(50) NOT NULL,
    "rank" SMALLINT,
    PRIMARY KEY ("code"),
    FOREIGN KEY ("main_activity_code") REFERENCES "ams_HAUL_ACTIVITIES"("code")
);

CREATE TABLE IF NOT EXISTS "ams_HAUL_ACTIVITY_CYCLES" (
    prev_code VARCHAR(50) NOT NULL,
    next_code VARCHAR(50) NOT NULL,
    PRIMARY KEY (prev_code, next_code),
    FOREIGN KEY (prev_code) REFERENCES "ams_HAUL_ACTIVITIES"("code"),
    FOREIGN KEY (next_code) REFERENCES "ams_HAUL_ACTIVITIES"("code")
);

INSERT INTO "ams_HAUL_STATUSES" (code, label, description, rank) VALUES
    ('READY', 'Ready', 'Ready for operation', 1),
    ('DELAY', 'Delay', 'Delayed operation', 2),
    ('IDLE', 'Idle', 'Idle state', 3),
    ('BREAKDOWN', 'Breakdown', 'Equipment breakdown', 4)
ON CONFLICT (code) DO UPDATE SET label = EXCLUDED.label, description = EXCLUDED.description, rank = EXCLUDED.rank;

INSERT INTO "ams_HAUL_ACTIVITIES" (code, label, description, is_load, haul_status_code, rank) VALUES
    ('TRAVELING_TO_LOADING', 'Traveling to Loading', 'Traveling to Loading', false, 'READY', null),
    ('QUEUEING_FOR_LOADING', 'Antri Loading', 'Queue for Loading', false, 'READY', null),
    ('LOADING', 'Loading', 'Loading Process', true, 'READY', null),
    ('TRAVELING_TO_WB_LOAD', 'Traveling to WB', 'Traveling to Weight Bridge (Loaded)', true, 'READY', null),
    ('QUEUEING_FOR_WB_LOAD', 'Antri WB ', 'Queue at Weight Bridge (Loaded)', true, 'READY', null),
    ('WEIGHING_THE_LOAD', 'Timbang Muatan', 'Weighing (Loaded)', true, 'READY', null),
    ('TRAVELING_TO_DUMPING', 'Traveling to Dumping', 'Traveling to Dumping', true, 'READY', null),
    ('QUEUEING_FOR_DUMPING', 'Antri Dumping', 'Queue for Dumping', true, 'READY', null),
    ('DUMPING', 'Dumping', 'Dumping Process', false, 'READY', null),
    ('TRAVELING_TO_WB_EMPTY', 'Traveling to WB', 'Traveling to Weight Bridge (Empty)', false, 'READY', null),
    ('QUEUEING_FOR_WB_EMPTY', 'Antri WB ', 'Queue at Weight Bridge (Empty)', false, 'READY', null),
    ('WEIGHING_EMPTY', 'Timbang Kosong', 'Weighing (Empty)', false, 'READY', null),

    ('CHANGE_SHIFT', 'Ganti Shift', 'Ganti Shift', NULL, 'DELAY', 0),
    ('PRE_OPERATION', 'Pra Operasi', 'Pra Operasi', NULL, 'DELAY', 1),
    ('REST_MEAL_PRAY', 'Ishoma', 'Ishoma', NULL, 'DELAY', 2),
    ('REFUELING', 'Refueling', 'Refueling', NULL, 'DELAY', 3),
    ('COALPAD_OBSTACLES', 'Kendala Coalpad', 'Kendala Coalpad', NULL, 'DELAY', 4),
    ('ROAD_OBSTACLES', 'Kendala Jalan', 'Kendala Jalan', NULL, 'DELAY', 5),
    ('WEIGHBRIDGE_OBSTACLES', 'Kendala WB', 'Kendala WB', NULL, 'DELAY', 6),
    ('SIDE_DUMP_OBSTACLES', 'Kendala Side Dump', 'Kendala Side Dump', NULL, 'DELAY', 7),
    ('INTERNAL', 'Internal', 'Internal', NULL, 'DELAY', 8),

    ('EXTERNAL', 'External', 'External', NULL, 'IDLE', 1),
    ('WEATHER', 'Cuaca', 'Weather', NULL, 'IDLE', 2),

    ('SCHEDULED_DOWN', 'Scheduled Down', 'Scheduled Down', NULL, 'BREAKDOWN', 1),
    ('UNSCHEDULED_DOWN', 'Unscheduled Down', 'Unscheduled Down', NULL, 'BREAKDOWN', 2)
ON CONFLICT (code) DO UPDATE SET label = EXCLUDED.label, description = EXCLUDED.description, rank = EXCLUDED.rank, is_load = EXCLUDED.is_load;

INSERT INTO "ams_HAUL_SUB_ACTIVITIES" (code, label, description, main_activity_code, rank) VALUES
    -- Pra Operasi sub-activities
    ('P5M', 'P5M', 'P5M', 'PRE_OPERATION', 1),
    ('SAFETY_TALK', 'Safety Talk', 'Safety Talk', 'PRE_OPERATION', 2),

    -- Ishoma sub-activities
    ('REST_FATIGUE', 'Fatigue', 'Fatigue', 'REST_MEAL_PRAY', 1),
    ('REST_MEAL', 'Meal', 'Meal', 'REST_MEAL_PRAY', 2),
    ('REST_PRAY', 'Pray', 'Pray', 'REST_MEAL_PRAY', 3),
    ('REST_TOILET', 'Toilet', 'Toilet', 'REST_MEAL_PRAY', 4),

    -- Refueling sub-activities
    ('WAIT_FUEL_STOCK', 'Tunggu Stock Fuel', 'Tunggu Stock Fuel', 'REFUELING', 1),
    ('QUEUE_REFUELING', 'Antrian Refueling', 'Antrian Refueling', 'REFUELING', 2),
    ('REFUELING_PROCESS', 'Refueling', 'Refueling', 'REFUELING', 3),

    -- Kendala Coalpad sub-activities
    ('WAIT_LOADER', 'Tunggu Loader', 'Tunggu Loader', 'COALPAD_OBSTACLES', 1),
    ('WAIT_LOADER_OPERATOR', 'Tunggu Operator Loader', 'Tunggu Operator Loader', 'COALPAD_OBSTACLES', 2),
    ('WAIT_INVENTORY', 'Tunggu Inventory', 'Tunggu Inventory', 'COALPAD_OBSTACLES', 3),

    -- Kendala Jalan sub-activities
    ('ROAD_REPAIR', 'Perbaikan Jalan', 'Perbaikan Jalan', 'ROAD_OBSTACLES', 1),
    ('FATIGUE_TEST_SAFETY', 'Fatigue Test (Safety Test)', 'Fatigue Test (Safety Test)', 'ROAD_OBSTACLES', 2),
    ('DUSTY_ROAD', 'Jalan Berdebu', 'Jalan Berdebu', 'ROAD_OBSTACLES', 3),
    ('INCIDENT', 'Ada Insiden', 'Ada Insiden', 'ROAD_OBSTACLES', 4),
    ('UNIT_BREAKDOWN', 'Ada Unit Breakdown', 'Ada Unit Breakdown', 'ROAD_OBSTACLES', 5),

    -- Kendala Jembatan Timbang sub-activities
    ('WEIGHBRIDGE_MAINTENANCE', 'WB Maintenance', 'Jembatan Timbang Maintenance', 'WEIGHBRIDGE_OBSTACLES', 1),
    ('WAIT_WEIGHBRIDGE_OFFICER', 'Tunggu Petugas WB', 'Tunggu Petugas Timbangan', 'WEIGHBRIDGE_OBSTACLES', 2),

    -- Kendala Side Dump sub-activities
    ('WAIT_DOZER', 'Tunggu Tongkang', 'Tunggu Tongkang', 'SIDE_DUMP_OBSTACLES', 1),
    ('WAIT_SIDE_DUMP_OFFICER', 'Tunggu Petugas Side Dump', 'Tunggu Petugas Side Dump', 'SIDE_DUMP_OBSTACLES', 2),
    ('SIDE_DUMP_PREPARATION', 'Persiapan Side Dump/ROM', 'Persiapan Side Dump/ROM', 'SIDE_DUMP_OBSTACLES', 3),
    ('SIDE_DUMP_FULL', 'Side Dump Penuh', 'Side Dump Penuh', 'SIDE_DUMP_OBSTACLES', 4),

    -- Internal sub-activities
    ('UNIT_WASH', 'Cuci Unit', 'Cuci Unit', 'INTERNAL',1),
    ('NO_OPERATOR', 'No Operator', 'No Operator', 'INTERNAL',2),
    ('UNIT_CHANGE_SHIFT', 'Unit Selip / Amblas', 'Unit Selip / Amblas', 'INTERNAL',3),  
    ('SOCIAL_PROBLEM', 'Masalah Sosial', 'Masalah Sosial', 'INTERNAL',4),

    -- External sub-activities
    ('VVIP_GUEST', 'Tamu VVIP', 'Tamu VVIP', 'EXTERNAL',1),
    ('CUSTOMER_ORDER', 'Customer Oder', 'Customer Oder', 'EXTERNAL',2),

    -- Cuaca sub-activities
    ('RAIN', 'Hujan', 'Hujan', 'WEATHER',1),
    ('FLOOD', 'Banjir', 'Banjir', 'WEATHER',2),
    ('LANDSLIDE', 'Tanah Longsor', 'Tanah Longsor', 'WEATHER',3),
    ('FIRE', 'Kebakaran', 'Kebakaran', 'WEATHER',4),

    -- Scheduled Down sub-activities
    ('PERIODIC_INSPECTION','Periodic Inspection', 'Periodic Inspection', 'SCHEDULED_DOWN', 1),
    ('SERVICE','Service', 'Service', 'SCHEDULED_DOWN', 2),
    ('GREASING','Greasing', 'Greasing', 'SCHEDULED_DOWN', 3),
    ('MID_LIFE_COMPONENT','Mid Life Component', 'Mid Life Component', 'SCHEDULED_DOWN', 4),
    ('GENERAL_OVER_HAUL','General Over Haul', 'General Over Haul', 'SCHEDULED_DOWN', 5),
    ('TYRE_INSPECTION','Tyre Inspection', 'Tyre Inspection', 'SCHEDULED_DOWN', 6),
    ('TYRE_ROLLING','Tyre Rolling', 'Tyre Rolling', 'SCHEDULED_DOWN', 7),
    ('GENERAL_TYRE_CHANGE','General Tyre Change', 'General Tyre Change', 'SCHEDULED_DOWN', 8),

    -- Unscheduled Down sub-activities
    ('ENGINE','Engine', 'Engine', 'UNSCHEDULED_DOWN', 1),
    ('HYDRAULIC_SYSTEM','Hydraulic System', 'Hydraulic System', 'UNSCHEDULED_DOWN', 2),
    ('POWERTRAIN_TORQUE_CODE','Powertrain (Transmisi)', 'Powertrain (Transmisi)', 'UNSCHEDULED_DOWN', 3),
    ('AXLE_DIFF_FINAL_DRIVE','Axle, Differential, Final Drive', 'Axle, Differential, Final Drive', 'UNSCHEDULED_DOWN', 4),
    ('STEERING_BRAKE','Steering & Brake', 'Steering & Brake', 'UNSCHEDULED_DOWN', 5),
    ('UNDERCARRIAGE','Undercarriage', 'Undercarriage', 'UNSCHEDULED_DOWN', 6),
    ('WHEEL_TYRE_RIM','Wheel (Tyre & Rim)', 'Wheel (Tyre & Rim)', 'UNSCHEDULED_DOWN', 7),
    ('PNEUMATIC_SYSTEM','Pneumatic System', 'Pneumatic System', 'UNSCHEDULED_DOWN', 8),
    ('ELECTRIC_SYSTEM','Electric System', 'Electric System', 'UNSCHEDULED_DOWN', 9),
    ('GENERAL_ATTACHMENT','General & Attachment', 'General & Attachment', 'UNSCHEDULED_DOWN', 10)
ON CONFLICT (code) DO UPDATE SET label = EXCLUDED.label, description = EXCLUDED.description, rank = EXCLUDED.rank;

INSERT INTO "ams_HAUL_ACTIVITY_CYCLES" (prev_code, next_code) VALUES
    ('TRAVELING_TO_LOADING', 'QUEUEING_FOR_LOADING'),
    ('QUEUEING_FOR_LOADING', 'LOADING'),
    ('LOADING', 'TRAVELING_TO_WB_LOAD'),
    ('TRAVELING_TO_WB_LOAD', 'QUEUEING_FOR_WB_LOAD'),
    ('QUEUEING_FOR_WB_LOAD', 'WEIGHING_THE_LOAD'),
    ('WEIGHING_THE_LOAD', 'TRAVELING_TO_DUMPING'),
    ('TRAVELING_TO_DUMPING', 'QUEUEING_FOR_DUMPING'),
    ('QUEUEING_FOR_DUMPING', 'DUMPING'),
    ('DUMPING', 'TRAVELING_TO_WB_EMPTY'),
    ('TRAVELING_TO_WB_EMPTY', 'QUEUEING_FOR_WB_EMPTY'),
    ('QUEUEING_FOR_WB_EMPTY', 'WEIGHING_EMPTY'),
    ('WEIGHING_EMPTY', 'TRAVELING_TO_LOADING')
ON CONFLICT (prev_code, next_code) DO NOTHING;


CREATE TABLE IF NOT EXISTS "ams_haulings" (
    id VARCHAR(40) PRIMARY KEY,
    asset_id VARCHAR(40) NOT NULL,
    operator_user_id VARCHAR(40),
    haul_status_code VARCHAR(20) NOT NULL,
    haul_activity_code VARCHAR(50) NOT NULL,  
    haul_sub_activity_code VARCHAR(50),
    start_time TIMESTAMPTZ NOT NULL,
    end_time TIMESTAMPTZ,

    client_id VARCHAR(40) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL,
    FOREIGN KEY (haul_status_code) REFERENCES "ams_HAUL_STATUSES"("code"),
    FOREIGN KEY (haul_activity_code) REFERENCES "ams_HAUL_ACTIVITIES"("code"),
    FOREIGN KEY (haul_sub_activity_code) REFERENCES "ams_HAUL_SUB_ACTIVITIES"("code")
);


CREATE INDEX IF NOT EXISTS idx_ams_haulings_asset_id ON "ams_haulings" ("asset_id") WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_ams_haulings_client_id ON "ams_haulings" ("client_id") WHERE deleted_at IS NULL;

INSERT INTO "ins_INTEGRATION_TYPE" (code, label, description)
VALUES ('DEVICE_TABLET', 'Device Tablet', 'Device Tablet')
ON CONFLICT (code) DO NOTHING;


CREATE TABLE IF NOT EXISTS "ams_driver_login_sessions" (
    id VARCHAR(40) PRIMARY KEY,
    asset_id VARCHAR(40) NOT NULL,
    user_id VARCHAR(40) NOT NULL,
    logged_in_time TIMESTAMPTZ NOT NULL,
    logged_out_time TIMESTAMPTZ,

    client_id VARCHAR(40) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL
);

CREATE INDEX IF NOT EXISTS idx_ams_driver_login_sessions_asset_id ON "ams_driver_login_sessions" ("asset_id") WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_ams_driver_login_sessions_client_id ON "ams_driver_login_sessions" ("client_id") WHERE deleted_at IS NULL;

ALTER TABLE "uis_user_clients"
ADD COLUMN IF NOT EXISTS "unencrypted_pin" VARCHAR(20);

ALTER TABLE "ams_driver_login_sessions" 
ADD COLUMN IF NOT EXISTS "user_full_name" VARCHAR(100);

COMMIT;
