package commonmodel

import (
	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
)

type FormReq struct {
	ReferenceID      string      `json:"reference_id" binding:"required"`
	FormCategoryCode string      `json:"form_category_code" binding:"required"`
	Fields           []FieldReq  `json:"fields" binding:"dive"`
	StatusCode       string      `json:"status_code"`
	TemplateID       null.String `json:"template_id"`
}

type FieldReq struct {
	ID              string       `json:"id"`
	FieldTypeCode   string       `json:"field_type_code" binding:"required"`
	Options         pgtype.JSONB `json:"options" binding:"required"`
	Sequence        int          `json:"sequence" binding:"min=1,required"`
	Label           string       `json:"label" binding:"required"`
	IsMandatory     null.Bool    `json:"is_mandatory"`
	Value           pgtype.JSONB `json:"value"`
	IsDelete        bool         `json:"is_delete"`
	TemplateFieldID null.String  `json:"template_field_id"`
}

func (r FieldReq) IsNew() bool {
	return r.ID == ""
}
