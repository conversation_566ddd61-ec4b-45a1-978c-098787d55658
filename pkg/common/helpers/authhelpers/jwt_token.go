package authhelpers

import (
	"assetfindr/internal/errorhandler"

	"context"
	"encoding/json"
	"fmt"

	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
)

type PermissionGroup struct {
	ID                      string            `json:"id"`
	GroupName               string            `json:"group_name"`
	Description             string            `json:"description"`
	PermissionGroupTypeCode string            `json:"permission_group_type_code"`
	PermissionRights        []PermissionRight `json:"permission_rights"`
}

type PermissionRight struct {
	PermissionCategoryCode string   `json:"permission_category_code"`
	IsEnable               bool     `json:"is_enable"`
	PermissionCodes        []string `json:"permission_codes"`
}

type PublicGaleryJwtTokenClaims struct {
	jwt.StandardClaims
	LoggedInClientID string `json:"logged_in_client_id"`
}

type JwtTokenClaims struct {
	jwt.StandardClaims
	UserID           string   `json:"user_id"`
	FirstName        string   `json:"first_name"`
	LastName         string   `json:"last_name"`
	Email            string   `json:"email"`
	FirebaseUserID   string   `json:"firebase_user_id"`
	LoggedInClientID string   `json:"logged_in_client_id"`
	ClientIDs        []string `json:"client_ids"`

	OptimaxPackageCodes []string `json:"optmx_pkg"`

	PermissionGroup PermissionGroup `json:"permission_group"`
}

func (j JwtTokenClaims) GetLoggedInClientID() string {
	if j.LoggedInClientID != "" {
		return j.LoggedInClientID
	}

	return j.ClientIDs[0]
}

func (j JwtTokenClaims) HasPackage(pkgs ...string) bool {
	for _, v := range j.OptimaxPackageCodes {
		for _, pkg := range pkgs {
			if pkg == v {
				return true
			}
		}
	}

	return false
}

func (j *JwtTokenClaims) GetPermissionCategory(category string) (*PermissionRight, error) {
	for _, val := range j.PermissionGroup.PermissionRights {
		if val.PermissionCategoryCode == category {
			return &val, nil
		}
	}
	return nil, errorhandler.ErrBadRequest("you not have permission")
}

func (j *JwtTokenClaims) IsHasPermission(category string, permissionCode string) bool {
	for _, permissionRight := range j.PermissionGroup.PermissionRights {
		if permissionRight.PermissionCategoryCode != category {
			continue
		}

		for i := range permissionRight.PermissionCodes {
			if permissionRight.PermissionCodes[i] == permissionCode {
				return true
			}
		}

		break
	}

	return false
}

func (j JwtTokenClaims) GetName() string {
	if j.LastName == "" {
		return j.FirstName
	}

	return j.FirstName + " " + j.LastName
}

func GetClaimsFromToken(token *jwt.Token) JwtTokenClaims {
	result := JwtTokenClaims{}
	rawMapClaims := token.Claims
	if rawMapClaims == nil {
		return result
	}
	mapClaims := rawMapClaims.(jwt.MapClaims)
	jsonString, _ := json.Marshal(mapClaims)

	json.Unmarshal(jsonString, &result)

	return result
}

const clientIDsField = "clientIDs"
const claimField = "clientIDs"

func GetClientIDsFromCtx(c *gin.Context) ([]string, error) {
	values, ok := c.Get(clientIDsField)
	if !ok {
		return nil, fmt.Errorf("clientIDs not found")
	}

	return values.([]string), nil
}

func SetClientIDsOnCtx(c *gin.Context, clientIDs []string) {
	c.Set(clientIDsField, clientIDs)
}

func GetClaimFromCtx(ctx context.Context) (JwtTokenClaims, error) {
	values, ok := ctx.Value("claim").(JwtTokenClaims)
	if !ok {
		return JwtTokenClaims{}, errorhandler.ErrUnauthorized
	}

	return values, nil
}

func GetClaimClientIdFromCtx(ctx context.Context) (string, error) {
	claim, err := GetClaimFromCtx(ctx)
	if err != nil {
		return "", err
	}

	return claim.LoggedInClientID, nil
}

func SetClaimToReqCtx(ctx context.Context, tokenClaim JwtTokenClaims) context.Context {
	return context.WithValue(ctx, "claim", tokenClaim)
}

type publicGaleryClaimKey string

const publicGalleryClaim publicGaleryClaimKey = "public_gallery_claim"

func GetPublicGalleryClaimFromCtx(ctx context.Context) (PublicGaleryJwtTokenClaims, error) {
	values, ok := ctx.Value(publicGaleryClaimKey(publicGalleryClaim)).(PublicGaleryJwtTokenClaims)
	if !ok {
		return PublicGaleryJwtTokenClaims{}, errorhandler.ErrUnauthorized
	}

	return values, nil
}

func SetPublicGalleryClaimToReqCtx(ctx context.Context, tokenClaim PublicGaleryJwtTokenClaims) context.Context {
	return context.WithValue(ctx, publicGaleryClaimKey(publicGalleryClaim), tokenClaim)
}
