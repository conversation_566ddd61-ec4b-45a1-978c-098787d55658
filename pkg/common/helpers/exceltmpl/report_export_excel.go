package exceltmpl

import (
	"assetfindr/pkg/common/commonlogger"
	"fmt"
	"time"

	"github.com/xuri/excelize/v2"
)

var (
	titleReportStyle = &excelize.Style{
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#F1F6FF"},
		},
		Font: &excelize.Font{
			Size: 27,
			Bold: true,
		},
	}

	downloadByReportStyle = &excelize.Style{
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#F1F6FF"},
		},
		Font: &excelize.Font{
			Size:   9,
			Color:  "#1F1F1F",
			Italic: true,
		},
	}
)

type ReportTempl struct {
	Title         string
	Logo          []byte
	LogoExtension string
	SheetName     string
	DownloadBy    string
	ClientName    string
	Description   string
	Columns       []ReportColumn
	Rows          [][]any
}

type ReportColumn struct {
	Name           string
	Width          int
	Style          *excelize.Style
	generatedStyle int
	row            string

	CustomDisplayLink string
}

func numberToExcelColumn(n int) string {
	if n < 0 {
		return "A"
	}

	result := ""
	n = n + 1 // Adjust to make 0 -> A, 1 -> B, etc.

	for n > 0 {
		n--
		result = string(byte('A'+(n%26))) + result
		n /= 26
	}

	return result
}

func (r *ReportTempl) ExportToExcelFile() (*excelize.File, error) {
	xlsx := excelize.NewFile()
	sheet := r.SheetName
	err := xlsx.SetSheetName(xlsx.GetSheetName(0), sheet)
	if err != nil {
		return nil, err
	}

	endColumn := numberToExcelColumn(len(r.Columns) - 1)
	if err := r.applyStylesAndHeader(xlsx, endColumn); err != nil {
		return nil, err
	}

	// Create Table Style
	tableTitleStyle, err := xlsx.NewStyle(tableTitleExcelizeStyle)
	if err != nil {
		commonlogger.Errorf("Error in styling to excel file", err.Error())
		return nil, fmt.Errorf("setting table title style: %w", err)
	}
	tableHeadeRow := 9

	err = xlsx.SetCellStyle(sheet, cell("A", tableHeadeRow), cell(endColumn, tableHeadeRow), tableTitleStyle)
	if err != nil {
		return nil, fmt.Errorf("setting table cell style: %w", err)
	}

	tableValStartRow := 10
	tableValEndRow := tableValStartRow + len(r.Rows)
	for i := range r.Columns {
		t := numberToExcelColumn(i)
		r.Columns[i].row = t
		col := r.Columns[i]

		if col.Width > 0 {
			err = xlsx.SetColWidth(sheet, col.row, col.row, float64(col.Width))
			if err != nil {
				return nil, err
			}
		}

		// if i == 0 {
		// 	err = xlsx.SetColWidth(sheet, col.row, col.row, 15)
		// 	if err != nil {
		// 		return nil, err
		// 	}
		// }

		err = xlsx.SetCellStr(sheet, cell(col.row, tableHeadeRow), col.Name)
		if err != nil {
			return nil, err
		}

		if col.Style != nil {
			style, err := xlsx.NewStyle(col.Style)
			if err != nil {
				return nil, err
			}
			err = xlsx.SetCellStyle(sheet, cell(col.row, tableValStartRow), cell(col.row, tableValEndRow), style)
			if err != nil {
				return nil, err
			}
		}
	}

	if len(r.Rows) > 0 {
		// Create Table Value Style
		tableValueStyle, err := xlsx.NewStyle(TableValExcelizeStyle)
		if err != nil {
			commonlogger.Errorf("Error in styling to excel file", err.Error())
			return nil, fmt.Errorf("setting table value style: %w", err)
		}

		err = xlsx.SetCellStyle(sheet, "A10", cell(endColumn, tableHeadeRow+1+len(r.Rows)), tableValueStyle)
		if err != nil {
			return nil, fmt.Errorf("setting download date style: %w", err)
		}

		for i, row := range r.Rows {
			for j, col := range r.Columns {
				if j >= len(row) {
					break
				}

				if row[j] == nil {
					err = xlsx.SetCellStr(sheet, cell(col.row, tableHeadeRow+1+i), "-")
					if err != nil {
						return nil, err
					}
					continue
				}

				err = setCellValue(xlsx, sheet, cell(col.row, tableHeadeRow+1+i), row[j])
				if err != nil {
					return nil, err
				}
			}
		}
	}

	return xlsx, nil
}
func setCellValue(f *excelize.File, sheet, cell string, value interface{}) error {
	var err error
	switch v := value.(type) {
	case string:
		if v == "" {
			v = "-"
		}
		err = f.SetCellStr(sheet, cell, v)
	default:
		err = f.SetCellValue(sheet, cell, v)
	}
	return err
}

func setCellValueLink(f *excelize.File, sheet, cell string, value interface{}, text string) error {
	var err error
	switch v := value.(type) {
	case string:
		if v == "" {
			text = "-"
		}

		err = f.SetCellStr(sheet, cell, text)
		if err != nil {
			return err
		}

		if text == "-" {
			return nil
		}

		if err := f.SetCellHyperLink(sheet, cell, v, "External", excelize.HyperlinkOpts{
			Tooltip: &text,
		}); err != nil {
			return err
		}
	default:
		err = f.SetCellValue(sheet, cell, v)
	}
	return err
}

func (r *ReportTempl) applyStylesAndHeader(xlsx *excelize.File, endColumn string) error {
	sheet := r.SheetName

	// Styles
	headerStyle, err := xlsx.NewStyle(headerExcelizeStyle)
	if err != nil {
		return fmt.Errorf("creating header style: %w", err)
	}

	titleStyle, err := xlsx.NewStyle(titleReportStyle)
	if err != nil {
		return fmt.Errorf("creating title style: %w", err)
	}

	downloadedByStyle, err := xlsx.NewStyle(downloadByReportStyle)
	if err != nil {
		return fmt.Errorf("creating download date style: %w", err)
	}

	// Apply Styles & Content
	if err := xlsx.SetCellStyle(sheet, "A1", endColumn+"8", headerStyle); err != nil {
		return fmt.Errorf("setting header style: %w", err)
	}

	if err := xlsx.MergeCell(sheet, "B2", endColumn+"4"); err != nil {
		return fmt.Errorf("merging title cells: %w", err)
	}

	if err := xlsx.SetCellStr(sheet, "B2", r.Title); err != nil {
		return fmt.Errorf("setting title text: %w", err)
	}
	if err := xlsx.SetCellStyle(sheet, "B2", "B2", titleStyle); err != nil {
		return fmt.Errorf("setting title style: %w", err)
	}

	if err := xlsx.SetColWidth(sheet, "A", "A", 10.8); err != nil {
		return fmt.Errorf("setting title style: %w", err)
	}

	if err := xlsx.MergeCell(sheet, "A2", "A5"); err != nil {
		return fmt.Errorf("merging title cells: %w", err)
	}

	if len(r.Logo) > 0 {
		if err := xlsx.AddPictureFromBytes(sheet, "A2", &excelize.Picture{
			File:      r.Logo,
			Extension: r.LogoExtension,
			Format:    &excelize.GraphicOptions{AutoFit: true},
		}); err != nil {
			return fmt.Errorf("adding logo image: %w", err)
		}
	} else {
		if err := xlsx.AddPicture(sheet, "A3", "./statics/AssetFindr-Logo-S2-Transparent-200.png", &excelize.GraphicOptions{ScaleX: 0.4, ScaleY: 0.4}); err != nil {
			return fmt.Errorf("adding digispect image: %w", err)
		}

	}

	// if err := xlsx.MergeCell(sheet, "B5", endColumn+"5"); err != nil {
	// 	return fmt.Errorf("merging download date cells: %w", err)
	// }
	if err := xlsx.SetCellStr(sheet, "B5", fmt.Sprintf("downloaded from AssetFindr by %s at %s", r.DownloadBy, time.Now().Local().Format("02-Jan-2006 15:04"))); err != nil {
		return fmt.Errorf("setting download date text: %w", err)
	}

	if err := xlsx.SetCellStyle(sheet, "B5", "B7", downloadedByStyle); err != nil {
		return fmt.Errorf("setting download date style: %w", err)
	}

	if err := xlsx.SetCellStr(sheet, "B6", r.ClientName); err != nil {
		return fmt.Errorf("setting client name text: %w", err)
	}

	if err := xlsx.SetCellStr(sheet, "B7", r.Description); err != nil {
		return fmt.Errorf("setting client name text: %w", err)
	}

	return nil
}

type HeaderFunc func(r *ReportTempl, xlsx *excelize.File, nCol int, endColumn string) error

func (r *ReportTempl) ExportToExcelFileOldStyle(headerFunc ...HeaderFunc) (*excelize.File, error) {
	xlsx := excelize.NewFile()
	sheet := r.SheetName
	err := xlsx.SetSheetName(xlsx.GetSheetName(0), sheet)
	if err != nil {
		return nil, err
	}

	endColumn := numberToExcelColumn(len(r.Columns) - 1)

	if len(headerFunc) > 0 {
		for _, f := range headerFunc {
			if err := f(r, xlsx, len(r.Columns), endColumn); err != nil {
				return nil, err
			}
		}
	} else {
		if err := r.applyStylesAndHeaderOldStyleDigispect(xlsx, endColumn); err != nil {
			return nil, err
		}
	}

	// Create Table Style
	tableTitleStyle, err := xlsx.NewStyle(tableTitleExcelizeStyle)
	if err != nil {
		commonlogger.Errorf("Error in styling to excel file", err.Error())
		return nil, fmt.Errorf("setting table title style: %w", err)
	}
	tableHeadeRow := 8

	err = xlsx.SetCellStyle(sheet, cell("A", tableHeadeRow), cell(endColumn, tableHeadeRow), tableTitleStyle)
	if err != nil {
		return nil, fmt.Errorf("setting table cell style: %w", err)
	}

	tableValStartRow := 9
	tableValEndRow := tableValStartRow + len(r.Rows)

	if len(r.Rows) > 0 {
		// Create Table Value Style
		tableValueStyle, err := xlsx.NewStyle(TableValExcelizeStyle)
		if err != nil {
			commonlogger.Errorf("Error in styling to excel file", err.Error())
			return nil, fmt.Errorf("setting table value style: %w", err)
		}

		err = xlsx.SetCellStyle(sheet, cell("A", tableValStartRow), cell(endColumn, tableHeadeRow+len(r.Rows)), tableValueStyle)
		if err != nil {
			return nil, fmt.Errorf("setting download date style: %w", err)
		}
	}

	for i := range r.Columns {
		t := numberToExcelColumn(i)
		r.Columns[i].row = t
		col := r.Columns[i]

		if col.Width > 0 {
			err = xlsx.SetColWidth(sheet, col.row, col.row, float64(col.Width))
			if err != nil {
				return nil, err
			}
		}

		// if i == 0 {
		// 	err = xlsx.SetColWidth(sheet, col.row, col.row, 15)
		// 	if err != nil {
		// 		return nil, err
		// 	}
		// }

		err = xlsx.SetCellStr(sheet, cell(col.row, tableHeadeRow), col.Name)
		if err != nil {
			return nil, err
		}

		if col.Style != nil {
			style, err := xlsx.NewStyle(col.Style)
			if err != nil {
				return nil, err
			}
			r.Columns[i].generatedStyle = style
			err = xlsx.SetCellStyle(sheet, cell(col.row, tableValStartRow), cell(col.row, tableValEndRow), style)
			if err != nil {
				return nil, err
			}
		}
	}

	if len(r.Rows) > 0 {
		for i, row := range r.Rows {
			for j, col := range r.Columns {
				if j >= len(row) {
					break
				}

				if row[j] == nil {
					err = xlsx.SetCellStr(sheet, cell(col.row, tableHeadeRow+1+i), "-")
					if err != nil {
						return nil, err
					}
					continue
				}

				if col.CustomDisplayLink != "" {
					err = setCellValueLink(xlsx, sheet, cell(col.row, tableHeadeRow+1+i), row[j], col.CustomDisplayLink)
					if err != nil {
						return nil, err
					}

					err = xlsx.SetCellStyle(sheet, cell(col.row, tableHeadeRow+1+i), cell(col.row, tableHeadeRow+1+i), col.generatedStyle)
					if err != nil {
						return nil, err
					}

					continue
				}

				err = setCellValue(xlsx, sheet, cell(col.row, tableHeadeRow+1+i), row[j])
				if err != nil {
					return nil, err
				}
			}
		}
	}

	return xlsx, nil
}

func (r *ReportTempl) applyStylesAndHeaderOldStyleDigispect(xlsx *excelize.File, endColumn string) error {
	sheet := r.SheetName

	// Styles
	headerStyle, err := xlsx.NewStyle(headerExcelizeStyle)
	if err != nil {
		return fmt.Errorf("creating header style: %w", err)
	}

	titleStyle, err := xlsx.NewStyle(titleExcelixeStyle)
	if err != nil {
		return fmt.Errorf("creating title style: %w", err)
	}

	downloadDateStyle, err := xlsx.NewStyle(downloadExcelizeDateStyle)
	if err != nil {
		return fmt.Errorf("creating download date style: %w", err)
	}

	// Apply Styles & Content
	if err := xlsx.SetCellStyle(sheet, "A1", endColumn+"7", headerStyle); err != nil {
		return fmt.Errorf("setting header style: %w", err)
	}

	if err := xlsx.MergeCell(sheet, "A2", endColumn+"4"); err != nil {
		return fmt.Errorf("merging title cells: %w", err)
	}

	if err := xlsx.SetCellStr(sheet, "A2", "Riwayat Inspeksi Ban"); err != nil {
		return fmt.Errorf("setting title text: %w", err)
	}
	if err := xlsx.SetCellStyle(sheet, "A2", "A2", titleStyle); err != nil {
		return fmt.Errorf("setting title style: %w", err)
	}

	if err := addImages(xlsx, sheet); err != nil {
		return err
	}

	downloadDate := fmt.Sprintf("downloaded by %s at %s", r.DownloadBy, time.Now().Local().Format("02-Jan-2006 15:04"))
	if err := xlsx.MergeCell(sheet, "A5", endColumn+"5"); err != nil {
		return fmt.Errorf("merging download date cells: %w", err)
	}
	if err := xlsx.SetCellStr(sheet, "A5", downloadDate); err != nil {
		return fmt.Errorf("setting download date text: %w", err)
	}
	if err := xlsx.SetCellStyle(sheet, "A5", "A5", downloadDateStyle); err != nil {
		return fmt.Errorf("setting download date style: %w", err)
	}

	// Create Table Style
	tableTitleStyle, err := xlsx.NewStyle(tableTitleExcelizeStyle)
	if err != nil {
		commonlogger.Errorf("Error in styling to excel file", err.Error())
		return fmt.Errorf("setting table title style: %w", err)
	}

	err = xlsx.SetCellStyle(sheet, "A8", endColumn+"8", tableTitleStyle)
	if err != nil {
		return fmt.Errorf("setting table cell style: %w", err)
	}

	return nil
}

func applyStylesAndHeaderOldStyleInspectionList(r *ReportTempl, xlsx *excelize.File, nCol int, endColumn string) error {
	sheet := r.SheetName

	// Styles
	headerStyle, err := xlsx.NewStyle(headerExcelizeStyle)
	if err != nil {
		return fmt.Errorf("creating header style: %w", err)
	}

	titleStyle, err := xlsx.NewStyle(titleExcelixeStyle)
	if err != nil {
		return fmt.Errorf("creating title style: %w", err)
	}

	downloadDateStyle, err := xlsx.NewStyle(downloadExcelizeDateStyle)
	if err != nil {
		return fmt.Errorf("creating download date style: %w", err)
	}

	// Apply Styles & Content
	if err := xlsx.SetCellStyle(sheet, "A1", endColumn+"7", headerStyle); err != nil {
		return fmt.Errorf("setting header style: %w", err)
	}

	if err := xlsx.MergeCell(sheet, "A2", endColumn+"4"); err != nil {
		return fmt.Errorf("merging title cells: %w", err)
	}

	if err := xlsx.SetCellStr(sheet, "A2", "Riwayat Inspeksi Ban"); err != nil {
		return fmt.Errorf("setting title text: %w", err)
	}
	if err := xlsx.SetCellStyle(sheet, "A2", "A2", titleStyle); err != nil {
		return fmt.Errorf("setting title style: %w", err)
	}

	if err := addImageLogo(xlsx, sheet, numberToExcelColumn(nCol-2)); err != nil {
		return err
	}

	downloadDate := fmt.Sprintf("downloaded by %s at %s", r.DownloadBy, time.Now().Local().Format("02-Jan-2006 15:04"))
	if err := xlsx.MergeCell(sheet, "A5", endColumn+"5"); err != nil {
		return fmt.Errorf("merging download date cells: %w", err)
	}
	if err := xlsx.SetCellStr(sheet, "A5", downloadDate); err != nil {
		return fmt.Errorf("setting download date text: %w", err)
	}
	if err := xlsx.SetCellStyle(sheet, "A5", "A5", downloadDateStyle); err != nil {
		return fmt.Errorf("setting download date style: %w", err)
	}

	// Create Table Style
	tableTitleStyle, err := xlsx.NewStyle(tableTitleExcelizeStyle)
	if err != nil {
		commonlogger.Errorf("Error in styling to excel file", err.Error())
		return fmt.Errorf("setting table title style: %w", err)
	}

	err = xlsx.SetCellStyle(sheet, "A8", endColumn+"8", tableTitleStyle)
	if err != nil {
		return fmt.Errorf("setting table cell style: %w", err)
	}

	return nil
}
