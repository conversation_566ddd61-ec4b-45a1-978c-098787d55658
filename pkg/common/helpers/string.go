package helpers

import "fmt"

func AddSuffixInvalid(s string) string {
	return s + " (invalid)"
}

func AddSuffixDuplicate(s string) string {
	return s + " (duplicate or already exist)"
}

func TruncateMiddle(text string, maxLength int) string {
	if len(text) <= maxLength {
		return text
	}

	half := maxLength / 2
	start := text[:half]
	end := text[len(text)-half:]

	return fmt.Sprintf("%s...%s", start, end)
}

func TruncateEnd(text string, maxLength int) string {
	if len(text) <= maxLength {
		return text
	}

	truncatedText := text[:maxLength-1]

	return fmt.Sprintf("%s...", truncatedText)
}

func StringOrDefaultIfEmpty(text string, defaultVal string) string {
	if text == "" {
		return defaultVal
	}

	return text
}
