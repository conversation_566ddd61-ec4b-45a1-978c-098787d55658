<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
      rel="stylesheet"
      type="text/css"
    />
    <style>
      body {
        font-family: "Poppins", sans-serif;
        margin: 0;
        padding: 0;
        background-color: #fff;
        color: #0c162c;
      }

      .page {
        width: 210mm;
        height: 297mm;
        margin: auto;
        background: white;
      }

      * {
        box-sizing: border-box;
      }

      .img-tyre {
        text-align: center;
        border-radius: 4px;
        width: 240px;
        height: 240px;
        margin: 8px 0;
        display: block;
      }

      p {
        margin: 0;
      }

      .text-center {
        text-align: center;
      }

      .text-xs {
        font-size: 12px;
      }

      .text-md {
        font-size: 16px;
      }

      .text-lg {
        font-size: 20px;
      }

      .font-bold {
        font-weight: 700;
      }

      .font-semibold {
        font-weight: 600;
      }

      .font-medium {
        font-weight: 500;
      }

      .text-blue-10 {
        color: #F1F6FF
      }

      .text-black-70 {
        color: #0C162C;
      }

      .text-grey-50 {
        color: #89869a;
      }

      .border-gray-100 {
        border: 1px solid #f3f4f6;
      }

      .my-2 {
        margin-top: 8px;
        margin-bottom: 8px;
      }

      .my-4 {
        margin-top: 16px;
        margin-bottom: 16px;
      }

      .mb-4 {
        margin-bottom: 16px;
      }

      .mb-2 {
        margin-bottom: 8px;
      }

      .p-2 {
        padding: 8px;
      }

      .py-2 {
        padding-top: 8px;
        padding-bottom: 8px;
      }

      .py-5 {
        padding-top: 20px;
        padding-bottom: 20px;
      }

      .pb-1 {
        padding-bottom: 4px;
      }

      .pb-2 {
        padding-bottom: 8px;
      }

      .pt-2 {
        padding-top: 8px;
      }

      .header-container:after {
        content: "";
        display: table;
        clear: both;
        margin-bottom: 12px;
      }

      .header-container__left {
        float: left;
        width: 30%;
      }

      .header-container__center {
        float: left;
        width: 40%;
        height: 70px;
        text-align: center;
        line-height: 70px;
      }

      .header-container__right {
        float: right;
        width: 30%;
        text-align: right;
      }

      .header-container__title {
        margin: 0;
        display: inline-block;
        vertical-align: middle;
        line-height: normal;
      }

      .detail-container__inspection:after {
        content: "";
        display: table;
        clear: both;
      }

      .detail-container__inspection-item {
        float: left;
        width: 50%;
      }

      .tyre-base-svg-container {
        margin: -52px 0;
      }

      .content-container-table {
        display: table; border: 1px solid #E7E8EA; width: 100%;
      }

      .content-container-table__row {
        display: table-row;
      }

      .content-container-table__cell {
        display: table-cell;
      }

      .content-container-table__cell-w30 {
        width: 30%;
      }

      .content-container-table__cell-w70 {
        width: 70%;
      }

      .content-container-table__cell-w100 {
        width: 100%;
      }

      .border-bottom-black-30 {
        border-bottom: 1px solid #E7E8EA;
      }

      .bg-blue-10 {
        background-color: #F1F6FF;
      }

      .p-6-px {
        padding: 6px;
      }

      .px-6-px {
        padding-left: 6px;
        padding-right: 6px;
      }

      .py-3-px {
        padding-top: 3px;
        padding-bottom: 3px;
      }

      .table-spacer {
        height: 24px;
      }

      .content-container-table__cell-text-spacer {
        height: 2px;
      }

      .content-container-table__cell-img {
        padding: 1px;
        height: 100px;
        width: 100px;
        object-fit: cover;
        border-radius: 12.5px;
      }

      .table-cell__tyre-base-container {
        float: left;
        width: 60%;
        text-align: right;
      }

      .table-cell__tyre-base-container-block {
        width: 240px;
        /* display: inline-block; */
      }

      .table-cell__pressure-container {
        float: right;
        width: 40%;
        text-align: left;
      }

      .table-cell__pressure-container-item {
        display: inline-block;
        margin-top: 8%;
        background-color: #185FFF;
        padding: 6px;
        width: fit-content;
        border-radius: 12px;
      }

      .table-cell__pressure-container-item-disable {
        display: inline-block;
        margin-top: 8%;
        background-color: #BABCC3;
        padding: 6px;
        width: fit-content;
        border-radius: 12px;
      }

      .table-cell__pressure-container-item-spacer {
        height: 3px;
      }

      .table-cell__pressure-container-img {
        height: 37px;
        width: 37px;
      }

      .text-lh-1-point-2 {
        line-height: 1.2;
      }

      svg {
        margin-top: 20px;
        margin-bottom: -36px;
      }

      .client-photo-wrap {
        display: -webkit-box; 
        -webkit-box-pack: center;
      }

      .finding-label-container {
        display: inline-block; 
        background-color: #e9e9e9; 
        border-radius: 4px; 
        padding: 4px; 
        margin-right: 4px;
      }

      @media print {
        body,
        .page {
          margin: 0;
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }

        .pagebreak-inside {
          page-break-inside: avoid;
        }

        @page {
          margin-top: 30px;
          margin-bottom: 0;
        }
      }
    </style>
  </head>
  <body>
    <div class="page">
      <!-- START OF HEADER -->
      <div class="header-container">
        <div class="header-container__left">
          <div class="client-photo-wrap">
            <img
              src="{{ .ClientPhoto }}"
              alt=""
              style="height: 82px"
            />
          </div>
        </div>
        <div class="header-container__center">
          <p class="text-lg font-bold header-container__title text-black-70">CATATAN PEMERIKSAAN</p>
        </div>
        <div class="header-container__right">
          {{ if ne .SecondaryClientPhoto "" }}
          <div class="client-photo-wrap">
            <img
              src="{{ .SecondaryClientPhoto }}"
              alt=""
              style="height: 82px"
            />
          </div>
          {{ end }}
        </div>
      </div>
      <!-- START OF HEADER -->
      <!-- START OF BODY -->
      <div>
        <div class="content-container-table">
          <div class="content-container-table__row bg-blue-10">
            <div class="content-container-table__cell content-container-table__cell-w30 p-6-px border-bottom-black-30">
              <p class="text-xs font-semibold text-black-70">Detail Inspeksi</p>
            </div>
            <div class="content-container-table__cell content-container-table__cell-w70 p-6-px border-bottom-black-30"></div>
          </div>
          <div class="content-container-table__row">
            <div class="content-container-table__cell content-container-table__cell-w30 p-6-px border-bottom-black-30">
              <p class="text-xs text-black-70">No. Inspeksi</p>
            </div>
            <div class="content-container-table__cell content-container-table__cell-w70 p-6-px border-bottom-black-30">
              <p class="text-xs text-black-70">{{ .AssetInspection.InspectionNumber }}</p>
            </div>
          </div>
          <div class="content-container-table__row">
            <div class="content-container-table__cell content-container-table__cell-w30 p-6-px border-bottom-black-30">
              <p class="text-xs text-black-70">No. Seri Ban</p>
            </div>
            <div class="content-container-table__cell content-container-table__cell-w70 p-6-px border-bottom-black-30">
              <p class="text-xs text-black-70">#{{ .SerialNo }}</p>
            </div>
          </div>
          {{ if ne .Rfid "" }}
          <div class="content-container-table__row">
            <div class="content-container-table__cell content-container-table__cell-w30 p-6-px border-bottom-black-30">
              <p class="text-xs text-black-70">RFID</p>
            </div>
            <div class="content-container-table__cell content-container-table__cell-w70 p-6-px border-bottom-black-30">
              <p class="text-xs text-black-70">
                {{ .Rfid }}
              </p>
            </div>
          </div>
          {{ end }}
          <div class="content-container-table__row">
            <div class="content-container-table__cell content-container-table__cell-w30 p-6-px">
              <p class="text-xs text-black-70">Tanggal</p>
            </div>
            <div class="content-container-table__cell content-container-table__cell-w70 p-6-px">
              <p class="text-xs text-black-70">
                {{ .InspectionDate }}
              </p>
            </div>
          </div>
        </div>
        <div class="table-spacer"></div>
        <div class="content-container-table">
          <div class="content-container-table__row bg-blue-10">
            <div class="content-container-table__cell content-container-table__cell-w30 p-6-px border-bottom-black-30">
              <p class="text-xs font-semibold text-black-70">Detail Ban</p>
            </div>
            <div class="content-container-table__cell content-container-table__cell-w70 p-6-px border-bottom-black-30"></div>
          </div>
          <div class="content-container-table__row">
            <div class="content-container-table__cell content-container-table__cell-w100 p-6-px border-bottom-black-30">
              <p class="text-xs text-grey-50">Merek / Ukuran</p>
              <div class="content-container-table__cell-text-spacer"></div>
              <p class="text-xs text-black-70">{{ .BrandName }} / {{ .TyreSize }}</p>
            </div>
            <div class="content-container-table__cell content-container-table__cell-w70 p-6-px border-bottom-black-30"></div>
          </div>
          <div class="content-container-table__row">
            <div class="content-container-table__cell content-container-table__cell-w100 p-6-px border-bottom-black-30">
              <div class="table-cell__tyre-base-container">
                <!-- START OF TREAD WAVE SECTION -->
                <div 
                  class="tread-wave-section" 
                  style="
                    display: -webkit-box;
                    -webkit-box-orient: horizontal;
                    -webkit-box-pack: end;
                    -webkit-box-align: center;
                    margin-right: 12px;
                  "
                >
                  <div class="tyrewave-parent-container" style="zoom: 0.9;">
                    <div style="width: {{ .TyreWaveSvg.TyreWaveWidth }}px;">
                      <!-- MM LABEL -->
                      <div
                        class="tread-uom"
                        style="
                          font-size: 12px;
                          text-align: center;
                          margin-bottom: 12px;
                          font-style: italic;
                          line-height: 18px;
                          color: #89869a;
                        "
                      >
                        Tapak Ban (mm)
                      </div>
                      <!-- TREAD VALUES -->
                      <div
                        class="tread-value"
                        style="
                          display: -webkit-box;
                          -webkit-box-pack: justify;
                          margin: 0 18px 10px 18px;
                        "
                      >
                        {{ range $idx, $item := .TyreWaveSvg.TreadDepths }}
                        <div
                          id="treadDepthsId-{{ $idx }}"
                          style="
                            background-color: white;
                            border: 1.5px solid #eef0f7;
                            border-radius: 8px;
                            color: black;
                            font-size: 12px;
                            font-weight: 400;
                            width: 38px;
                            height: 28px;
                            text-align: center;
                            line-height: 26px;
                          "
                        >
                          {{ if gt $item 0.0 }}
                            {{ printf "%.1f" $item }} 
                          {{ else }}
                            -
                          {{ end }}
                        </div>
                        {{ end }}
                      </div>
                      <!-- TREAD WAVE -->
                      <div
                        class="tyre-tread"
                        style="
                          display: -webkit-box;
                          -webkit-box-pack: justify;
                          width: 100%;
                          height: 30px;
                          max-height: 30px;
                          margin-bottom: 2px;
                        "
                      >
                        {{ range .TyreWaveSvg.TyreTreads }}
                        <div style="margin-top: -20px;">
                          {{ .Svg }}
                        </div>
                        {{ end }}
                      </div>
                      <!-- Tread Base -->
                      <div style="display: -webkit-box; margin-top: -20px;">
                        <div
                          style="
                            position: absolute;
                            width: {{ .TyreWaveSvg.TyreWaveWidth }}px;
                            display: -webkit-box;
                            -webkit-box-pack: center;
                            margin-top: 20px;
                          "
                        >
                          <div
                            style="
                              color: black;
                              font-size: 10px;
                              font-weight: 400;
                              line-height: 18px;
                            "
                          >
                            {{ if .TyreWaveSvg.ShowOTD }}
                            OTD: {{ .TyreWaveSvg.OTDLabel }}
                            {{ end }}
                          </div>
                        </div>
                  
                        <div class="base-tread" style="width: 100%;">
                          {{ .TyreWaveSvg.BaseTread }}
                        </div>
                      </div>
                      <!-- Average RTD -->
                      {{ if gt .TyreWaveSvg.NoOfInspectionPoint 3 }}
                      <div style="margin-top: 16px;"></div>
                      {{ else }}
                      <div style="margin-top: 28px;"></div>
                      {{ end }}
                      <div>
                        <p class="text-xs text-center">
                          <span class="text-grey-50">Rata-rata&nbsp:&nbsp</span>
                          <span class="text-black-70">
                            {{ if eq .AverageRTD 0.0 }} 
                              -
                            {{ else }}
                              {{ printf "%.1f" .AverageRTD }}
                              {{ if .TyreWaveSvg.ShowOTD }}
                                {{ if gt .RemainingTUR 0.0 }}
                                  ({{ printf "%.0f" .RemainingTUR }}%)
                                {{ end }}
                              {{ end }}
                            {{ end }}
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- END OF TREAD WAVE SECTION -->
              </div>
              <div class="table-cell__pressure-container">
                {{ if gt .InspectionTyre.Pressure 0.0 }}
                <div class="table-cell__pressure-container-item">
                  <img class="table-cell__pressure-container-img" src="{{ .PressureEnabledImg }}" alt="">
                  <div class="table-cell__pressure-container-item-spacer"></div>
                  <p class="text-xs text-blue-10 font-semibold text-center text-lh-1-point-2">
                    {{ printf "%.0f" .InspectionTyre.Pressure }} 
                  </p>
                  <p class="text-xs text-blue-10 text-center text-lh-1-point-2">psi</p>
                  <div class="table-cell__pressure-container-item-spacer"></div>
                </div>
                {{ else }}
                <div class="table-cell__pressure-container-item-disable">
                  <img class="table-cell__pressure-container-img" src="{{ .PressureDisabledImg }}" alt="">
                  <div class="table-cell__pressure-container-item-spacer"></div>
                  <p class="text-xs text-black-70 font-semibold text-center text-lh-1-point-2">
                    -
                  </p>
                  <p class="text-xs text-black-70 text-center text-lh-1-point-2">psi</p>
                  <div class="table-cell__pressure-container-item-spacer"></div>
                </div>
                {{ end }}
              </div>
            </div>
            <div class="content-container-table__cell content-container-table__cell-w70 p-6-px border-bottom-black-30"></div>
          </div>
          <div class="content-container-table__row">
            <div class="content-container-table__cell content-container-table__cell-w100 p-6-px border-bottom-black-30">
              <p class="text-xs text-grey-50">Gambar</p>
              <div class="content-container-table__cell-text-spacer"></div>
              {{ if gt (len .Attachments) 0 }}
              <div class="content-container-table__cell-img-container">
                {{ range .Attachments }}
                <img class="content-container-table__cell-img" src="{{ .Path }}" alt="Attachment">
                {{ end }}
              </div>
              {{ else }}
              <p class="text-xs text-black-70">-</p>
              {{ end }}
            </div>
            <div class="content-container-table__cell content-container-table__cell-w70 p-6-px border-bottom-black-30"></div>
          </div>
          <div class="content-container-table__row">
            <div class="content-container-table__cell content-container-table__cell-w100 p-6-px border-bottom-black-30">
              <p class="text-xs text-grey-50">Catatan</p>
              <div class="content-container-table__cell-text-spacer"></div>
              <p class="text-xs text-black-70">
                {{ if .InspectionTyre.Remark }}
                  {{ .InspectionTyre.Remark }}
                {{ else }}
                  -
                {{ end }}
              </p>
            </div>
            <div class="content-container-table__cell content-container-table__cell-w70 p-6-px border-bottom-black-30"></div>
          </div>
          <div class="content-container-table__row">
            <div class="content-container-table__cell content-container-table__cell-w100 p-6-px">
              <p class="text-xs text-grey-50">Temuan / Kondisi Ban</p>
              <div class="content-container-table__cell-text-spacer"></div>
              {{ if gt (len .FindingLabels) 0 }}
              <span>
                {{ range .FindingLabels }}
                <div class="finding-label-container">
                  <p class="text-xs text-black-70">{{ . }}</p>
                </div>
                {{ end }}
              </span>
              {{ else }}
                <p class="text-xs text-black-70">-</p>
              {{ end }}
            </div>
            <div class="content-container-table__cell content-container-table__cell-w70 p-6-px border-bottom-black-30"></div>
          </div>
        </div>
      </div>
      <!-- END OF BODY -->
    </div>
  </body>
</html>
